# Quick fix for the ARIMA dimension mismatch issue
# Add this code cell to your notebook to fix the training error

def fixed_forward(self, x: torch.Tensor, return_components: bool = False):
    """
    Fixed forward pass that handles dimension mismatches correctly.
    """
    batch_size, seq_len = x.shape

    # Input validation
    if torch.isnan(x).any():
        raise ValueError("Input contains NaN values")
    if torch.isinf(x).any():
        raise ValueError("Input contains infinite values")
    if seq_len < max(self.p, self.q) + self.d + 1:
        raise ValueError(f"Input sequence too short: {seq_len} < {max(self.p, self.q) + self.d + 1}")

    # Apply differencing
    x_diff = self.apply_differencing(x)
    diff_len = x_diff.size(1)

    # Calculate the effective start index for predictions
    start_idx = max(self.p, self.q)

    # Initialize outputs with correct dimensions
    # Only predict from start_idx onwards to avoid dimension mismatches
    pred_len = diff_len - start_idx
    predictions = torch.zeros(batch_size, pred_len, device=self.device)
    errors = torch.zeros_like(x_diff)

    # Enforce parameter constraints
    self.enforce_stationarity_constraints()
    self.enforce_invertibility_constraints()

    # ARIMA computation with proper indexing
    for i, t in enumerate(range(start_idx, diff_len)):
        # Initialize prediction with intercept
        pred = self.intercept.expand(batch_size)

        # AR component: φ₁x_{t-1} + φ₂x_{t-2} + ... + φₚx_{t-p}
        if self.p > 0:
            # Get AR terms in reverse order for proper coefficient alignment
            ar_terms = x_diff[:, t-self.p:t].flip(dims=[1])
            ar_contribution = torch.sum(self.ar_params.unsqueeze(0) * ar_terms, dim=1)
            pred = pred + ar_contribution

        # MA component: θ₁ε_{t-1} + θ₂ε_{t-2} + ... + θₑε_{t-q}
        if self.q > 0 and t >= self.q:
            # Get MA terms in reverse order for proper coefficient alignment
            ma_terms = errors[:, t-self.q:t].flip(dims=[1])
            ma_contribution = torch.sum(self.ma_params.unsqueeze(0) * ma_terms, dim=1)
            pred = pred + ma_contribution

        # Store prediction at correct index
        predictions[:, i] = pred

        # Compute innovation/error: ε_t = x_t - ŷ_t
        error = x_diff[:, t] - pred
        errors[:, t] = error

    if return_components:
        components = {
            'errors': errors,
            'differenced_input': x_diff,
            'predictions_full': predictions,
            'start_idx': start_idx,
            'ar_params': self.ar_params.detach().cpu().numpy() if self.ar_params is not None else None,
            'ma_params': self.ma_params.detach().cpu().numpy() if self.ma_params is not None else None,
            'intercept': self.intercept.detach().cpu().item(),
            'sigma': torch.exp(self.log_sigma).detach().cpu().item()
        }
        return predictions, components

    return predictions

def fixed_train_model(self, train_data, val_data, epochs=1000, patience=100, gradient_clip=1.0):
    """
    Fixed training method that handles dimension mismatches correctly.
    """
    if self.model is None:
        self.create_model()

    print(f"🎯 Starting training for {epochs} epochs with patience {patience}")
    print(f"📊 Model parameters: {self.model.count_parameters()}")
    print(f"🔧 Device: {self.device}")

    # Training state
    best_val_loss = float('inf')
    patience_counter = 0
    train_losses = []
    val_losses = []
    learning_rates = []

    # Loss function
    criterion = nn.MSELoss()

    # Test forward pass first to catch dimension issues early
    print("🧪 Testing forward pass...")
    try:
        with torch.no_grad():
            test_pred = self.model(train_data[:, :100])  # Test with smaller sequence
            print(f"✅ Forward pass test successful. Output shape: {test_pred.shape}")
    except Exception as e:
        print(f"❌ Forward pass test failed: {e}")
        raise RuntimeError(f"Model forward pass is broken: {e}")

    try:
        # Use manual progress tracking instead of tqdm for better visibility
        print(f"\n🚀 Starting training loop...")

        for epoch in range(epochs):
            # Training phase
            self.model.train()
            self.optimizer.zero_grad()

            try:
                # Forward pass
                train_pred = self.model(train_data)

                # Calculate loss with proper dimension handling
                start_idx = max(self.p, self.q)
                target_data = train_data[:, start_idx:]

                # Ensure dimensions match
                if train_pred.size(1) != target_data.size(1):
                    min_len = min(train_pred.size(1), target_data.size(1))
                    train_pred = train_pred[:, :min_len]
                    target_data = target_data[:, :min_len]

                train_loss = criterion(train_pred, target_data)

                # Check for NaN/infinite loss
                if train_loss.isnan() or train_loss.isinf():
                    print(f"❌ Invalid loss at epoch {epoch}: {train_loss}")
                    break

                # Backward pass with gradient clipping
                train_loss.backward()

                # Gradient clipping (import from torch.nn.utils)
                from torch.nn.utils import clip_grad_norm_
                grad_norm = clip_grad_norm_(self.model.parameters(), gradient_clip)
                self.optimizer.step()

            except Exception as e:
                print(f"❌ Training step failed at epoch {epoch}: {e}")
                break

            # Validation phase
            self.model.eval()
            with torch.no_grad():
                try:
                    val_pred = self.model(val_data)
                    val_target = val_data[:, start_idx:]

                    # Ensure dimensions match for validation
                    if val_pred.size(1) != val_target.size(1):
                        min_len = min(val_pred.size(1), val_target.size(1))
                        val_pred = val_pred[:, :min_len]
                        val_target = val_target[:, :min_len]

                    val_loss = criterion(val_pred, val_target)

                    if val_loss.isnan() or val_loss.isinf():
                        print(f"❌ Invalid validation loss at epoch {epoch}: {val_loss}")
                        break

                except Exception as e:
                    print(f"❌ Validation step failed at epoch {epoch}: {e}")
                    break

            # Record metrics
            train_losses.append(train_loss.item())
            val_losses.append(val_loss.item())
            learning_rates.append(self.optimizer.param_groups[0]['lr'])

            # Early stopping and model checkpointing
            if val_loss < best_val_loss:
                best_val_loss = val_loss.item()
                patience_counter = 0

                # Save best model state
                self.best_model_state = {
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch,
                    'val_loss': best_val_loss
                }
            else:
                patience_counter += 1

            # Learning rate scheduling
            self.scheduler.step(val_loss)

            # Print progress every 50 epochs or at key milestones
            if epoch % 50 == 0 or epoch < 10 or patience_counter == 0:
                print(f"Epoch {epoch:4d} | Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f} | Best: {best_val_loss:.6f} | LR: {self.optimizer.param_groups[0]['lr']:.2e} | Patience: {patience_counter}")

            # Early stopping
            if patience_counter >= patience:
                print(f"⏰ Early stopping at epoch {epoch}")
                break

        # Load best model
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state['model_state_dict'])
            print(f"✅ Loaded best model from epoch {self.best_model_state['epoch']}")

        # Store training history
        self.training_history = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'learning_rates': learning_rates,
            'best_val_loss': best_val_loss,
            'epochs_trained': len(train_losses),
            'converged': patience_counter < patience
        }

        self.is_trained = True

        print(f"🎉 Training completed! Best validation loss: {best_val_loss:.6f}")
        print(f"📊 Epochs trained: {len(train_losses)}")
        print(f"🎯 Converged: {patience_counter < patience}")

        return self.training_history

    except Exception as e:
        print(f"❌ Training failed: {e}")
        raise RuntimeError(f"Training failed: {e}")

# Apply the fixes - these will be applied when the notebook imports this
print("🔧 Preparing dimension mismatch fixes...")

def apply_fixes():
    """Apply the fixes to the existing classes"""
    try:
        # Get the classes from the global namespace
        import sys

        # Find the classes in the current module/notebook
        for name, obj in globals().items():
            if hasattr(obj, '__name__'):
                if 'ProductionARIMA' in str(obj):
                    obj.forward = fixed_forward
                    print(f"✅ Fixed forward method for {name}")
                elif 'ProductionFinancialForecaster' in str(obj):
                    obj.train_model = fixed_train_model
                    print(f"✅ Fixed training method for {name}")

        print("✅ All fixes applied successfully!")
        return True

    except Exception as e:
        print(f"⚠️ Could not auto-apply fixes: {e}")
        print("📝 Please manually apply the fixes in your notebook")
        return False

# Try to apply fixes automatically
apply_fixes()

print("\n� Manual Application Instructions:")
print("If auto-fix didn't work, add these lines to your notebook:")
print("ProductionARIMA.forward = fixed_forward")
print("ProductionFinancialForecaster.train_model = fixed_train_model")
