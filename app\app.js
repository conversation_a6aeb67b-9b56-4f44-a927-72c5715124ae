// Financial Reports ML Dashboard JavaScript

// Configuration
const API_BASE_URL = 'http://localhost:8001';
let charts = {};
let currentConfig = {
    metric: 'revenue',
    period: 'daily',
    data_source: 'full_dataset',
    sample_part: null
};
let selectedHorizon = null;
let apiConnected = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Financial Reports ML Dashboard initialized');
    initializeApp();
});

// Initialize application
async function initializeApp() {
    showLoading(true);

    try {
        // Check API status
        await checkApiStatus();

        // Load initial data
        await loadDashboardData();

        // Initialize charts
        initializeCharts();

        console.log('Dashboard initialized successfully');
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        showNotification('Error', 'Failed to initialize dashboard. Please check if the API server is running.', 'error');
    } finally {
        showLoading(false);
    }
}

// API Status Check
async function checkApiStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/`);
        const data = await response.json();

        const statusElement = document.getElementById('apiStatus');
        if (response.ok) {
            statusElement.className = 'api-status online';
            statusElement.innerHTML = '<i class="fas fa-circle"></i><span>API Online</span>';
        } else {
            throw new Error('API not responding');
        }
    } catch (error) {
        const statusElement = document.getElementById('apiStatus');
        statusElement.className = 'api-status offline';
        statusElement.innerHTML = '<i class="fas fa-circle"></i><span>API Offline</span>';
        throw error;
    }
}

// Check API connection
async function checkApiConnection() {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

        const response = await fetch(`${API_BASE_URL}/`, {
            method: 'GET',
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const health = await response.json();
            apiConnected = true;
            console.log('✅ API connected:', health);

            // Update status indicator
            const statusElement = document.getElementById('forecastStatus');
            if (statusElement) {
                statusElement.innerHTML = `
                    <span style="color: var(--success-color);">
                        <i class="fas fa-check-circle"></i>
                        API Connected - Ready for forecasting
                    </span>
                `;
            }

            // Hide instruction card and show chart
            const instructionsElement = document.getElementById('apiInstructions');
            const chartCanvas = document.getElementById('smartForecastChart');
            if (instructionsElement && chartCanvas) {
                instructionsElement.style.display = 'none';
                chartCanvas.style.display = 'block';
            }

            return true;
        } else {
            throw new Error(`API responded with status: ${response.status}`);
        }
    } catch (error) {
        console.error('❌ API connection failed:', error);
        apiConnected = false;
        showApiConnectionError();
        return false;
    }
}

// Show API connection error
function showApiConnectionError() {
    const statusElement = document.getElementById('forecastStatus');
    if (statusElement) {
        statusElement.innerHTML = `
            <div style="color: var(--error-color); text-align: center; padding: 1rem;">
                <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <div><strong>API Server Not Running</strong></div>
                <div style="font-size: 0.9rem; margin-top: 0.5rem;">
                    Start the API server with:<br>
                    <code style="background: rgba(0,0,0,0.1); padding: 0.25rem; border-radius: 4px;">python basic_api.py</code>
                </div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.8;">
                    Then refresh this page
                </div>
            </div>
        `;
    }

    // Show instruction card in chart area
    const instructionsElement = document.getElementById('apiInstructions');
    const chartCanvas = document.getElementById('smartForecastChart');
    if (instructionsElement && chartCanvas) {
        instructionsElement.style.display = 'flex';
        chartCanvas.style.display = 'none';
    }

    // Disable forecast button
    const generateBtn = document.getElementById('generateForecastBtn');
    if (generateBtn) {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> API Required';
    }

    // Show notification with instructions
    showNotification('API Server Required',
        'To use forecasting features, start the API server:\n\n1. Open terminal in app/ folder\n2. Run: python basic_api.py\n3. Refresh this page',
        'error');
}

// Load Dashboard Data
async function loadDashboardData() {
    try {
        // Check API connection first
        const apiOk = await checkApiConnection();
        if (!apiOk) {
            return;
        }

        // Load database statistics (Enhanced API provides this via model-status)
        // const statsResponse = await fetch(`${API_BASE_URL}/database-stats`);
        // if (statsResponse.ok) {
        //     const stats = await statsResponse.json();
        //     updateStatistics(stats);
        // }

        // Load model information
        const modelResponse = await fetch(`${API_BASE_URL}/model-status`);
        if (modelResponse.ok) {
            const modelInfo = await modelResponse.json();
            updateModelInfo(modelInfo);
        }

        // Load prediction options (Enhanced API doesn't need this)
        // await loadPredictionOptions();

    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Update Statistics Cards
function updateStatistics(stats) {
    document.getElementById('totalRevenue').textContent = formatCurrency(stats.total_revenue || 0);
    document.getElementById('totalTransactions').textContent = formatNumber(stats.total_transactions || 0);
    document.getElementById('uniqueUsers').textContent = formatNumber(stats.unique_users || 0);
    document.getElementById('uniqueCountries').textContent = formatNumber(stats.unique_countries || 0);
}

// Update Model Information
function updateModelInfo(modelInfo) {
    console.log('📊 Updating model info:', modelInfo);

    const modelInfoElement = document.getElementById('modelInfo');
    const modelParamsElement = document.getElementById('modelParams');

    if (modelInfo.model_loaded) {
        modelInfoElement.innerHTML = `
            <p><strong>Status:</strong> <span style="color: var(--success-color);">Model Loaded</span></p>
            <p><strong>Last Data Point:</strong> ${modelInfo.last_data_point || 'N/A'}</p>
            <p><strong>Data Points:</strong> ${formatNumber(modelInfo.data_points || 0)}</p>
        `;

        if (modelInfo.model_params) {
            const [p, d, q] = modelInfo.model_params;

            // Ensure AIC and BIC are properly formatted
            const aicValue = modelInfo.aic !== null && modelInfo.aic !== undefined ?
                (typeof modelInfo.aic === 'number' ? modelInfo.aic.toFixed(2) : modelInfo.aic) : 'N/A';
            const bicValue = modelInfo.bic !== null && modelInfo.bic !== undefined ?
                (typeof modelInfo.bic === 'number' ? modelInfo.bic.toFixed(2) : modelInfo.bic) : 'N/A';

            console.log(`📈 Model metrics - AIC: ${aicValue}, BIC: ${bicValue}`);

            modelParamsElement.innerHTML = `
                <div class="param-item">
                    <div class="param-label">ARIMA Order</div>
                    <div class="param-value">(${p}, ${d}, ${q})</div>
                </div>
                <div class="param-item">
                    <div class="param-label">AIC</div>
                    <div class="param-value">${aicValue}</div>
                </div>
                <div class="param-item">
                    <div class="param-label">BIC</div>
                    <div class="param-value">${bicValue}</div>
                </div>
            `;
        } else {
            console.warn('⚠️ No model parameters available');
            modelParamsElement.innerHTML = `
                <div class="param-item">
                    <div class="param-label">Model Parameters</div>
                    <div class="param-value">Not Available</div>
                </div>
            `;
        }
    } else {
        modelInfoElement.innerHTML = `
            <p><strong>Status:</strong> <span style="color: var(--warning-color);">No Model Loaded</span></p>
            <p>Click "Train Model" to create a new ARIMA model.</p>
        `;
        modelParamsElement.innerHTML = '';
    }
}

// Initialize Charts
function initializeCharts() {
    // Initialize forecast chart (use existing smartForecastChart)
    const forecastCtx = document.getElementById('smartForecastChart');
    if (!forecastCtx) {
        console.warn('Chart canvas not found, skipping chart initialization');
        return;
    }
    const ctx = forecastCtx.getContext('2d');
    charts.forecast = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Forecast',
                data: [],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: true,
                tension: 0.4
            }, {
                label: 'Confidence Interval',
                data: [],
                borderColor: 'rgba(37, 99, 235, 0.3)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: '+1',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });


}

// Update Forecast Chart
async function updateForecast() {
    const days = document.getElementById('forecastDays').value;
    const forecastInfo = document.getElementById('forecastInfo');

    showLoading(true);

    try {
        const response = await fetch(`${API_BASE_URL}/forecast`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                steps: parseInt(days),
                confidence_level: 0.95
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.forecast && data.forecast.length > 0) {
            const labels = data.dates.map(date => formatDate(date));
            const forecasts = data.forecast;
            const upperCI = data.confidence_intervals.map(ci => ci[1]);
            const lowerCI = data.confidence_intervals.map(ci => ci[0]);

            charts.forecast.data.labels = labels;
            charts.forecast.data.datasets[0].data = forecasts;
            charts.forecast.data.datasets[1].data = upperCI;
            charts.forecast.update();

            forecastInfo.innerHTML = `
                <p><strong>Forecast Generated:</strong> ${days} days ahead</p>
                <p><strong>Model:</strong> ARIMA${data.model_info.model_params ? `(${data.model_info.model_params.join(', ')})` : ''}</p>
                <p><strong>Confidence Level:</strong> ${(data.metadata.confidence_interval * 100).toFixed(0)}%</p>
            `;
        } else {
            throw new Error('No forecast data received');
        }

    } catch (error) {
        console.error('Error updating forecast:', error);
        forecastInfo.innerHTML = `<p style="color: var(--error-color);">Error: ${error.message}</p>`;
        showNotification('Forecast Error', `Failed to generate forecast: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}



// Train Model
async function trainModel() {
    showLoading(true);

    try {
        const response = await fetch(`${API_BASE_URL}/train`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('🎯 Training result:', result);

        showNotification('Success', 'Model trained successfully!', 'success');

        // Refresh model info to get updated AIC/BIC values
        await loadDashboardData();

    } catch (error) {
        console.error('Error training model:', error);
        showNotification('Training Error', `Failed to train model: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Test API Endpoint
async function testEndpoint(endpoint) {
    const responseElement = document.getElementById('apiResponse');

    try {
        responseElement.textContent = 'Loading...';

        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        const data = await response.json();

        responseElement.textContent = JSON.stringify(data, null, 2);

    } catch (error) {
        responseElement.textContent = `Error: ${error.message}`;
    }
}

// Refresh Data
async function refreshData() {
    showLoading(true);

    try {
        await loadDashboardData();
        // await updateHistoricalChart(); // Enhanced API doesn't need this
        showNotification('Success', 'Data refreshed successfully!', 'success');
    } catch (error) {
        console.error('Error refreshing data:', error);
        showNotification('Error', 'Failed to refresh data', 'error');
    } finally {
        showLoading(false);
    }
}

// Utility Functions
function formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
}

function formatNumber(value) {
    return new Intl.NumberFormat('en-US').format(value);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
    });
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
        overlay.classList.add('show');
    } else {
        overlay.classList.remove('show');
    }
}

function showNotification(title, message, type = 'info') {
    const modal = document.getElementById('notificationModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');

    modalTitle.textContent = title;
    modalMessage.textContent = message;

    // Add type-specific styling
    modal.className = `modal show ${type}`;

    modal.classList.add('show');
}

function closeModal() {
    const modal = document.getElementById('notificationModal');
    modal.classList.remove('show');
}

function showDocumentation() {
    window.open(`${API_BASE_URL}/docs`, '_blank');
}

function showAbout() {
    showNotification(
        'About Financial Reports ML Dashboard',
        'This dashboard provides real-time analytics and ARIMA-based forecasting for financial transaction data. Built with FastAPI, Chart.js, and modern web technologies.',
        'info'
    );
}



// ===== INTERACTIVE FORECASTING FUNCTIONS =====

// Load prediction options (Enhanced API doesn't need this)
async function loadPredictionOptions() {
    try {
        // Enhanced API doesn't have prediction-options endpoint
        console.log('Enhanced API: Skipping prediction options');

        // Load available sample parts
        // await loadAvailableSampleParts();

        updatePredictionOptions();
    } catch (error) {
        console.error('Error loading prediction options:', error);
    }
}

// Load available sample parts
async function loadAvailableSampleParts() {
    console.log('🔍 Loading available sample parts...');

    try {
        console.log(`📡 Fetching from: ${API_BASE_URL}/available-sample-parts`);
        const response = await fetch(`${API_BASE_URL}/available-sample-parts`);

        console.log(`📊 Response status: ${response.status}`);

        if (response.ok) {
            const result = await response.json();
            console.log('📦 API Response:', result);

            if (result.success && result.available_parts) {
                console.log(`✅ Found ${result.available_parts.length} sample parts`);
                updateSamplePartOptions(result.available_parts);
                return;
            } else {
                console.warn('⚠️ No sample parts available:', result);
            }
        } else {
            console.error(`❌ Failed to load sample parts: ${response.status} ${response.statusText}`);
        }
    } catch (error) {
        console.error('❌ Error loading sample parts:', error);
    }

    // Fallback: Create default sample parts if API fails
    console.log('🔄 Using fallback sample parts...');
    const fallbackParts = [];
    const fallbackMonths = [
        '2019-02', '2019-01', '2018-09', '2018-05', '2018-04',
        '2018-07', '2018-10', '2018-03', '2018-06', '2018-12',
        '2018-11', '2018-08', '2019-03', '2018-02', '2018-01'
    ];

    for (let i = 1; i <= 15; i++) {
        fallbackParts.push({
            part_number: i,
            transaction_count: 100,
            metadata: {
                time_period: {
                    month: fallbackMonths[i-1] || `2018-${String(i).padStart(2, '0')}`
                }
            }
        });
    }
    updateSamplePartOptions(fallbackParts);
}

// Update sample part dropdown options
function updateSamplePartOptions(availableParts) {
    console.log('🔧 Updating sample part options...', availableParts);

    const samplePartSelect = document.getElementById('samplePart');

    if (!samplePartSelect) {
        console.error('❌ Sample part select element not found');
        return;
    }

    console.log('✅ Found sample part select element');

    // Clear existing options
    samplePartSelect.innerHTML = '';

    if (!availableParts || availableParts.length === 0) {
        samplePartSelect.innerHTML = '<option value="">No sample parts available</option>';
        console.warn('⚠️ No sample parts available to display');
        return;
    }

    console.log(`📊 Processing ${availableParts.length} sample parts`);

    // Add default option
    samplePartSelect.innerHTML = '<option value="">Select a sample part...</option>';

    // Add available parts
    availableParts.forEach((part, index) => {
        console.log(`📦 Processing part ${index + 1}:`, part);

        const option = document.createElement('option');
        option.value = part.part_number;

        const metadata = part.metadata || {};
        const timePeriod = metadata.time_period || {};
        const month = timePeriod.month || 'Unknown';

        option.textContent = `Part ${part.part_number} - ${month} (${part.transaction_count} transactions)`;
        samplePartSelect.appendChild(option);

        console.log(`✅ Added option: ${option.textContent}`);
    });

    console.log(`✅ Updated sample part options with ${availableParts.length} parts`);
}

// Update prediction options display
function updatePredictionOptions() {
    const metric = document.getElementById('predictionMetric').value;
    const period = document.getElementById('predictionPeriod').value;
    const dataSource = document.getElementById('dataSource').value;
    const samplePart = document.getElementById('samplePart').value;

    // Show/hide sample part selection based on data source
    const samplePartGroup = document.getElementById('samplePartGroup');
    if (dataSource === 'sample_data') {
        samplePartGroup.style.display = 'block';
    } else {
        samplePartGroup.style.display = 'none';
    }

    // Update help text based on metric
    const metricHelp = document.getElementById('metricHelp');
    const metricDescriptions = {
        'revenue': 'Predict total revenue for financial planning and growth analysis',
        'transaction_count': 'Predict customer activity for capacity planning and staffing',
        'quantity': 'Predict demand for inventory management and supply chain planning'
    };
    metricHelp.textContent = metricDescriptions[metric] || '';

    // Update current config
    currentConfig = {
        metric,
        period,
        data_source: dataSource,
        sample_part: dataSource === 'sample_data' && samplePart ? parseInt(samplePart) : null
    };

    // Update summary display
    updateForecastSummary();

    // Configure prediction on backend
    configurePrediction();
}

// Update forecast summary display
function updateForecastSummary() {
    document.getElementById('selectedMetric').textContent =
        currentConfig.metric.charAt(0).toUpperCase() + currentConfig.metric.slice(1).replace('_', ' ');
    document.getElementById('selectedPeriod').textContent =
        currentConfig.period.charAt(0).toUpperCase() + currentConfig.period.slice(1);

    // Update data source display with sample part info
    let dataText = currentConfig.data_source === 'full_dataset' ? 'Full Dataset' : 'Sample Data';
    if (currentConfig.data_source === 'sample_data' && currentConfig.sample_part) {
        dataText += ` (Part ${currentConfig.sample_part})`;
    }
    document.getElementById('selectedData').textContent = dataText;

    const horizonText = selectedHorizon || 'Select a time horizon';
    document.getElementById('selectedHorizon').textContent = horizonText;

    // Enable/disable generate button
    const generateBtn = document.getElementById('generateForecastBtn');
    generateBtn.disabled = !selectedHorizon;
}

// Configure prediction on backend
async function configurePrediction() {
    try {
        // Check API connection first
        if (!apiConnected) {
            const apiOk = await checkApiConnection();
            if (!apiOk) {
                return;
            }
        }

        showLoading(true);

        const response = await fetch(`${API_BASE_URL}/configure-prediction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(currentConfig)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Prediction configured:', result);

            // Update forecast status
            const statusElement = document.getElementById('forecastStatus');
            statusElement.innerHTML = `
                <span style="color: var(--success-color);">
                    <i class="fas fa-check-circle"></i>
                    Model trained (${result.data_points} data points)
                </span>
            `;

            showNotification('Success', 'Prediction model configured and trained successfully!', 'success');
        } else {
            const error = await response.json();
            throw new Error(error.error || 'Configuration failed');
        }
    } catch (error) {
        console.error('Error configuring prediction:', error);
        showNotification('Error', `Failed to configure prediction: ${error.message}`, 'error');

        const statusElement = document.getElementById('forecastStatus');
        statusElement.innerHTML = `
            <span style="color: var(--error-color);">
                <i class="fas fa-exclamation-circle"></i>
                Configuration failed
            </span>
        `;
    } finally {
        showLoading(false);
    }
}

// Switch horizon tabs
function switchHorizonTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Show/hide content
    document.querySelectorAll('.horizon-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(`${tabName}Horizon`).classList.remove('hidden');
}

// Select horizon from quick options
function selectHorizon(horizon) {
    selectedHorizon = horizon;
    updateForecastSummary();

    // Visual feedback
    document.querySelectorAll('.horizon-btn').forEach(btn => {
        btn.style.background = '';
        btn.style.color = '';
        btn.style.borderColor = '';
    });
    event.target.style.background = 'var(--primary-color)';
    event.target.style.color = 'white';
    event.target.style.borderColor = 'var(--primary-color)';
}

// Select custom horizon
function selectCustomHorizon() {
    const days = document.getElementById('customDays').value;
    const unit = document.getElementById('customUnit').value;

    if (!days || days <= 0) {
        showNotification('Error', 'Please enter a valid number', 'error');
        return;
    }

    selectedHorizon = `${days} ${unit}`;
    updateForecastSummary();

    showNotification('Success', `Selected: ${selectedHorizon}`, 'success');
}

// Select natural language horizon
function selectNaturalHorizon() {
    const input = document.getElementById('naturalInput').value.trim();

    if (!input) {
        showNotification('Error', 'Please enter a time horizon', 'error');
        return;
    }

    selectedHorizon = input;
    updateForecastSummary();

    showNotification('Success', `Selected: ${selectedHorizon}`, 'success');
}

// Generate smart forecast
async function generateSmartForecast() {
    if (!selectedHorizon) {
        showNotification('Error', 'Please select a time horizon first', 'error');
        return;
    }

    try {
        showLoading(true);

        const response = await fetch(`${API_BASE_URL}/generate-forecast`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                steps: parseInt(selectedHorizon) || 30,
                confidence_level: 0.95
            })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Forecast generated:', result);

            // Enhanced API returns forecast data directly
            if (result.forecast && result.forecast.length > 0) {
                displayForecastResults(result);
                showNotification('Success', 'Enhanced forecast generated successfully!', 'success');
            } else {
                throw new Error('No forecast data received');
            }
        } else {
            const error = await response.json();
            throw new Error(error.error || 'Forecast request failed');
        }
    } catch (error) {
        console.error('Error generating forecast:', error);
        showNotification('Error', `Failed to generate forecast: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Display forecast results
function displayForecastResults(forecast) {
    console.log('📊 Displaying forecast results:', forecast);

    // Update chart
    updateSmartForecastChart(forecast);

    // Update insights
    updateForecastInsights(forecast);

    // Update status
    const statusElement = document.getElementById('forecastStatus');
    statusElement.innerHTML = `
        <span style="color: var(--success-color);">
            <i class="fas fa-chart-line"></i>
            ${description} generated successfully
        </span>
    `;
}

// Update smart forecast chart
function updateSmartForecastChart(forecast) {
    console.log('📈 Updating smart forecast chart:', forecast);

    if (!charts.smartForecast) {
        initializeSmartForecastChart();
    }

    const chart = charts.smartForecast;

    // Prepare data with dynamic time formatting (Enhanced API format)
    let labels = forecast.dates || forecast.forecast_dates;
    const forecastData = forecast.forecast || forecast.forecast_values;
    const lowerCI = forecast.confidence_intervals ? forecast.confidence_intervals.map(ci => ci[0]) : forecast.lower_ci;
    const upperCI = forecast.confidence_intervals ? forecast.confidence_intervals.map(ci => ci[1]) : forecast.upper_ci;

    // For tomorrow predictions, create hourly labels
    const description = forecast.description || `${forecastData.length}-step forecast`;
    if (description.toLowerCase().includes('tomorrow') && forecastData.length === 1) {
        console.log('🕐 Creating hourly breakdown for tomorrow prediction');

        // Create 24 hourly data points for tomorrow
        const hourlyLabels = [];
        const hourlyData = [];
        const hourlyUpper = [];
        const hourlyLower = [];

        const baseValue = forecastData[0];
        const baseUpper = upperCI[0];
        const baseLower = lowerCI[0];

        // Generate hourly variation (simulate intraday patterns)
        for (let hour = 0; hour < 24; hour++) {
            hourlyLabels.push(`${hour.toString().padStart(2, '0')}:00`);

            // Add realistic hourly variation (business hours typically higher)
            const hourlyMultiplier = getHourlyMultiplier(hour);
            hourlyData.push(baseValue * hourlyMultiplier);
            hourlyUpper.push(baseUpper * hourlyMultiplier);
            hourlyLower.push(baseLower * hourlyMultiplier);
        }

        labels = hourlyLabels;
        chart.data.datasets[0].data = hourlyData;
        chart.data.datasets[1].data = hourlyUpper;
        chart.data.datasets[2].data = hourlyLower;

    } else {
        // Format labels for multi-day forecasts
        labels = labels.map(date => {
            if (typeof date === 'string') {
                return formatDate(date);
            }
            return date;
        });

        chart.data.datasets[0].data = forecastData;
        chart.data.datasets[1].data = upperCI;
        chart.data.datasets[2].data = lowerCI;
    }

    // Update chart data
    chart.data.labels = labels;

    // Update chart title
    chart.options.plugins.title.text = `${currentConfig.metric.toUpperCase()} Forecast: ${forecast.description}`;

    // Update x-axis label based on forecast type
    const xAxisLabel = forecast.description.toLowerCase().includes('tomorrow') ? 'Hour of Day' : 'Date';
    chart.options.scales.x.title.text = xAxisLabel;

    console.log('📊 Chart updated with', labels.length, 'data points');
    chart.update();
}

// Helper function to get hourly multipliers for realistic intraday patterns
function getHourlyMultiplier(hour) {
    // Business hours pattern: higher activity during 9-17, lower at night
    const businessHours = [
        0.3, 0.2, 0.1, 0.1, 0.2, 0.4, 0.6, 0.8, // 00-07: Night/Early morning
        1.0, 1.2, 1.3, 1.4, 1.5, 1.4, 1.3, 1.2, // 08-15: Business hours
        1.1, 1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4  // 16-23: Evening
    ];

    return businessHours[hour] || 1.0;
}

// Initialize smart forecast chart
function initializeSmartForecastChart() {
    const ctx = document.getElementById('smartForecastChart').getContext('2d');

    charts.smartForecast = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Forecast',
                data: [],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: false,
                tension: 0.4,
                pointRadius: 3
            }, {
                label: 'Upper CI',
                data: [],
                borderColor: 'rgba(37, 99, 235, 0.3)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: '+1',
                tension: 0.4,
                pointRadius: 0
            }, {
                label: 'Lower CI',
                data: [],
                borderColor: 'rgba(37, 99, 235, 0.3)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: false,
                tension: 0.4,
                pointRadius: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Smart Forecast Results'
                },
                legend: {
                    display: true
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: 'Value'
                    },
                    ticks: {
                        callback: function(value) {
                            if (currentConfig.metric === 'revenue') {
                                return formatCurrency(value);
                            } else {
                                return formatNumber(value);
                            }
                        }
                    }
                }
            }
        }
    });
}

// Update forecast insights
function updateForecastInsights(forecast) {
    const insightsContainer = document.getElementById('forecastInsights');

    let insightsHTML = `
        <div class="insight-item">
            <div class="insight-icon">📊</div>
            <div class="insight-content">
                <div class="insight-label">Total Forecast</div>
                <div class="insight-value">${formatForecastValue(forecast.total_forecast)}</div>
            </div>
        </div>
        <div class="insight-item">
            <div class="insight-icon">📈</div>
            <div class="insight-content">
                <div class="insight-label">Growth Rate</div>
                <div class="insight-value">${forecast.growth_rate > 0 ? '+' : ''}${forecast.growth_rate.toFixed(1)}%</div>
            </div>
        </div>
        <div class="insight-item">
            <div class="insight-icon">⚡</div>
            <div class="insight-content">
                <div class="insight-label">Average ${currentConfig.period.charAt(0).toUpperCase() + currentConfig.period.slice(1)}</div>
                <div class="insight-value">${formatForecastValue(forecast.average_forecast)}</div>
            </div>
        </div>
    `;

    // Add business insights
    if (forecast.insights && forecast.insights.length > 0) {
        forecast.insights.forEach(insight => {
            insightsHTML += `
                <div class="insight-item">
                    <div class="insight-icon">${insight.icon}</div>
                    <div class="insight-content">
                        <div class="insight-label">${insight.title}</div>
                        <div class="insight-value">${insight.message}</div>
                    </div>
                </div>
            `;
        });
    }

    insightsContainer.innerHTML = insightsHTML;
}

// Format forecast value based on metric
function formatForecastValue(value) {
    if (currentConfig.metric === 'revenue') {
        return formatCurrency(value);
    } else {
        return formatNumber(Math.round(value));
    }
}

// Initialize smart forecast chart when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeSmartForecastChart();
    }, 1000);
});
