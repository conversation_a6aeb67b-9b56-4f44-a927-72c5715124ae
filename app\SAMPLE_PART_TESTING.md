# 🧪 Individual Sample Part Testing for ARIMA Models

## 📋 Overview

The app/ now supports **individual sample part selection** for ARIMA model testing, allowing you to test the model with specific time periods and data characteristics instead of using the entire dataset or just part1.json.

## 🎯 Why Individual Sample Part Testing?

### **Benefits**
- ✅ **Targeted Testing**: Test model performance on specific time periods
- ✅ **Robustness Validation**: Evaluate model across different seasonal patterns
- ✅ **Performance Comparison**: Compare accuracy across various data characteristics
- ✅ **Faster Iteration**: Quick testing with smaller, focused datasets
- ✅ **Debugging**: Isolate issues to specific time periods or data patterns

### **Use Cases**
1. **Seasonal Analysis**: Compare model performance across different months
2. **Data Quality Testing**: Test with high vs low transaction volume periods
3. **Model Validation**: Ensure consistent performance across time periods
4. **Development**: Quick testing during model development and tuning

## 📊 Available Sample Parts

Each sample part contains **100 transactions** from different time periods:

| Part | Time Period | Month | Characteristics |
|------|-------------|-------|----------------|
| part1.json | 2019-02-01 to 2019-02-20 | 2019-02 | Recent data, winter period |
| part2.json | 2019-01-04 to 2019-01-30 | 2019-01 | New year period, post-holiday |
| part3.json | 2018-09-01 to 2018-09-30 | 2018-09 | Back-to-school season |
| part4.json | 2018-05-01 to 2018-05-30 | 2018-05 | Spring period |
| part5.json | 2018-04-01 to 2018-04-30 | 2018-04 | Easter season |
| part6.json | 2018-07-01 to 2018-07-31 | 2018-07 | Summer period |
| part7.json | 2018-10-01 to 2018-10-31 | 2018-10 | Halloween/autumn |
| part8.json | 2018-03-02 to 2018-03-31 | 2018-03 | Early spring |
| part9.json | 2018-06-01 to 2018-06-30 | 2018-06 | Early summer |
| part10.json | 2018-12-01 to 2018-12-31 | 2018-12 | Holiday season |

## 🌐 Web Interface Usage

### **Step 1: Select Data Source**
1. Open the web dashboard at `http://localhost:3000`
2. In the **Prediction Configuration** section
3. Set **Data Source** to "📦 Sample Data"
4. The **Sample Part** dropdown will appear automatically

### **Step 2: Choose Sample Part**
1. Click the **Sample Part** dropdown
2. Select from available parts (e.g., "Part 3 - 2018-09 (100 transactions)")
3. Each option shows:
   - Part number
   - Time period (month)
   - Transaction count

### **Step 3: Configure and Test**
1. Select your desired **Metric** (Revenue, Transaction Count, Quantity)
2. Choose **Period** (Daily, Weekly, Monthly)
3. The system will automatically:
   - Load the selected sample part
   - Train the ARIMA model
   - Display model performance metrics

## 🔧 API Usage

### **Configure with Specific Sample Part**
```bash
POST /configure-prediction
Content-Type: application/json

{
    "metric": "revenue",
    "period": "daily",
    "data_source": "sample_data",
    "sample_part": 3
}
```

### **Get Available Sample Parts**
```bash
GET /available-sample-parts

Response:
{
    "success": true,
    "available_parts": [
        {
            "part_number": 1,
            "file_path": "../sample/part1.json",
            "transaction_count": 100,
            "metadata": {
                "part_number": 1,
                "time_period": {
                    "month": "2019-02",
                    "start_date": "2019-02-01",
                    "end_date": "2019-02-20"
                },
                "sample_size": 100
            }
        }
    ],
    "total_parts": 10,
    "message": "Found 10 sample parts available for testing"
}
```

## 🐍 Python Script Usage

### **Direct Testing**
```python
from arima_api import SmartARIMAForecaster

# Initialize forecaster
forecaster = SmartARIMAForecaster()

# Test with specific sample part
success = forecaster.load_data(
    metric='revenue',
    period='daily',
    data_source='sample_data',
    sample_part=5  # Use part5.json (April 2018)
)

if success:
    # Train and validate model
    forecaster.train_model()
    results = forecaster.validate_model()
    print(f"Accuracy: {results['accuracy']:.1f}%")
```

### **Automated Testing Script**
```bash
cd app/
python test_sample_parts.py
```

This script will:
- Test all available sample parts automatically
- Compare performance across different time periods
- Rank parts by accuracy
- Provide recommendations for usage

## 📈 Performance Analysis

### **Expected Performance Variations**
Different sample parts may show varying performance due to:

- **Seasonal Patterns**: Holiday periods vs regular months
- **Data Volume**: Transaction density variations
- **Market Conditions**: Economic factors during different periods
- **Data Quality**: Outliers or anomalies in specific periods

### **Typical Results**
```
🏆 PERFORMANCE RANKING:
Rank Part Period      Accuracy  MAPE    RMSE      Transactions
--------------------------------------------------------------
1    <USER>    <GROUP>     78.5%     21.5%   45.23     100
2    3    2018-09     76.2%     23.8%   52.18     100
3    6    2018-07     74.1%     25.9%   48.67     100
4    1    2019-02     72.3%     27.7%   55.42     100
5    10   2018-12     69.8%     30.2%   62.15     100
```

## 🎯 Best Practices

### **For Development**
1. **Start with high-accuracy parts** for initial model validation
2. **Use multiple parts** to test robustness
3. **Compare seasonal vs non-seasonal** periods
4. **Test edge cases** with lower-performing parts

### **For Production**
1. **Validate on multiple parts** before deployment
2. **Monitor performance** across different time periods
3. **Use ensemble approaches** combining multiple part results
4. **Regular re-testing** as new data becomes available

### **For Research**
1. **Systematic testing** across all available parts
2. **Statistical analysis** of performance variations
3. **Feature engineering** based on part-specific insights
4. **Model improvement** targeting weak-performing periods

## 🚀 Advanced Usage

### **Batch Testing**
```python
# Test multiple parts in sequence
parts_to_test = [1, 3, 5, 7, 10]
results = {}

for part in parts_to_test:
    forecaster.load_data('revenue', 'daily', 'sample_data', part)
    forecaster.train_model()
    results[part] = forecaster.validate_model()
```

### **Comparative Analysis**
```python
# Compare performance across metrics
metrics = ['revenue', 'transaction_count', 'quantity']
part_performance = {}

for metric in metrics:
    forecaster.load_data(metric, 'daily', 'sample_data', 3)
    forecaster.train_model()
    part_performance[metric] = forecaster.validate_model()
```

## 🔍 Troubleshooting

### **Common Issues**
1. **"Sample part not found"**: Ensure sample/ directory exists with part files
2. **"Training failed"**: Some parts may have insufficient data for certain periods
3. **"Low accuracy"**: Normal variation - some periods are harder to predict

### **Solutions**
1. **Check file existence**: Verify all part1.json through part10.json exist
2. **Try different parts**: Some may work better than others
3. **Adjust parameters**: Consider different ARIMA orders for specific parts

## 📝 Summary

Individual sample part testing provides:
- **Granular control** over testing data
- **Better understanding** of model behavior
- **Improved validation** across different scenarios
- **Faster development** cycles
- **More robust** model evaluation

This enhancement makes the ARIMA forecasting system more flexible and suitable for comprehensive model testing and validation.
