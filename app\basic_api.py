#!/usr/bin/env python3
"""
Basic API Server - Minimal dependencies, maximum compatibility
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
import threading
import time
from datetime import datetime

class APIHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Set CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # Route handling
        if path == '/health':
            response = {
                'status': 'healthy',
                'message': 'Basic API is running',
                'timestamp': datetime.now().isoformat()
            }
        
        elif path == '/prediction-options':
            response = {
                'metrics': {
                    'revenue': {
                        'name': 'Revenue',
                        'description': 'Total transaction value (financial performance)',
                        'icon': '💰'
                    },
                    'transaction_count': {
                        'name': 'Transaction Count',
                        'description': 'Number of transactions (customer activity)',
                        'icon': '📊'
                    },
                    'quantity': {
                        'name': 'Quantity',
                        'description': 'Total items purchased (demand forecasting)',
                        'icon': '📦'
                    }
                },
                'periods': {
                    'daily': {'name': 'Daily', 'description': 'High granularity'},
                    'weekly': {'name': 'Weekly', 'description': 'Medium granularity'},
                    'monthly': {'name': 'Monthly', 'description': 'Low granularity'}
                },
                'data_sources': {
                    'full_dataset': {'name': 'Full Dataset', 'description': 'Complete data'},
                    'sample_data': {'name': 'Sample Data', 'description': 'Sample data'}
                }
            }
        
        elif path == '/model-status':
            response = {
                'model_loaded': True,
                'config': {
                    'metric': 'revenue',
                    'period': 'daily',
                    'data_source': 'full_dataset'
                },
                'model_params': [1, 1, 1],
                'data_points': 100,
                'last_data_point': '2024-01-01'
            }
        
        elif path == '/database-stats':
            response = {
                'total_transactions': 20000,
                'total_revenue': 4237053.36,
                'unique_users': 1500,
                'unique_countries': 25
            }
        
        else:
            response = {'error': 'Endpoint not found'}
        
        # Send response
        self.wfile.write(json.dumps(response).encode())
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Read request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode()) if post_data else {}
        except:
            request_data = {}
        
        # Set CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # Route handling
        if path == '/configure-prediction':
            metric = request_data.get('metric', 'revenue')
            period = request_data.get('period', 'daily')
            data_source = request_data.get('data_source', 'full_dataset')
            
            response = {
                'success': True,
                'message': 'Configuration updated successfully',
                'config': {
                    'metric': metric,
                    'period': period,
                    'data_source': data_source
                },
                'data_points': 100,
                'model_params': [1, 1, 1]
            }
        
        elif path == '/generate-forecast':
            user_input = request_data.get('horizon', 'next month')
            
            # Simple horizon parsing
            if 'tomorrow' in user_input.lower():
                steps = 1
                description = "Tomorrow"
            elif 'week' in user_input.lower():
                steps = 7
                description = "Next Week"
            elif 'month' in user_input.lower():
                steps = 30
                description = "Next Month"
            elif 'year' in user_input.lower():
                steps = 365
                description = "Next Year"
            else:
                steps = 30
                description = "Next 30 Days"
            
            # Generate mock forecast data
            import random
            base_value = 1000
            forecast_values = []
            forecast_dates = []
            lower_ci = []
            upper_ci = []
            
            for i in range(min(steps, 30)):
                value = base_value + random.randint(-100, 100)
                forecast_values.append(value)
                forecast_dates.append(f"2024-01-{i+1:02d}")
                lower_ci.append(value - 50)
                upper_ci.append(value + 50)
            
            total_forecast = sum(forecast_values)
            average_forecast = total_forecast / len(forecast_values)
            growth_rate = random.uniform(-10, 15)
            
            # Generate insights
            insights = []
            if growth_rate > 5:
                insights.append({
                    'type': 'positive',
                    'icon': '📈',
                    'title': 'Growth Expected',
                    'message': f'{growth_rate:.1f}% growth forecasted'
                })
            elif growth_rate < -5:
                insights.append({
                    'type': 'warning',
                    'icon': '📉',
                    'title': 'Decline Expected',
                    'message': f'{abs(growth_rate):.1f}% decline forecasted'
                })
            else:
                insights.append({
                    'type': 'neutral',
                    'icon': '📊',
                    'title': 'Stable Trend',
                    'message': 'Steady performance expected'
                })
            
            response = {
                'success': True,
                'forecast': {
                    'user_input': user_input,
                    'description': description,
                    'steps': len(forecast_values),
                    'forecast_dates': forecast_dates,
                    'forecast_values': forecast_values,
                    'lower_ci': lower_ci,
                    'upper_ci': upper_ci,
                    'total_forecast': total_forecast,
                    'average_forecast': average_forecast,
                    'growth_rate': growth_rate,
                    'insights': insights,
                    'config': {
                        'metric': 'revenue',
                        'period': 'daily',
                        'data_source': 'full_dataset'
                    }
                }
            }
        
        else:
            response = {'success': False, 'error': 'Endpoint not found'}
        
        # Send response
        self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override to reduce log noise"""
        return

def start_api_server():
    """Start the API server"""
    PORT = 8001
    
    try:
        with socketserver.TCPServer(("", PORT), APIHandler) as httpd:
            print("🚀 Basic ARIMA API Server Started")
            print(f"🌐 API available at: http://localhost:{PORT}")
            print("📊 Endpoints available:")
            print("   • GET  /health")
            print("   • GET  /prediction-options")
            print("   • POST /configure-prediction")
            print("   • POST /generate-forecast")
            print("   • GET  /model-status")
            print("   • GET  /database-stats")
            print()
            print("🛑 Press Ctrl+C to stop the server")
            print("=" * 60)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 API server stopped")
    except Exception as e:
        print(f"❌ Error starting API server: {e}")

if __name__ == "__main__":
    start_api_server()
