#!/usr/bin/env python3
"""
Test script to verify AIC/BIC display and chart functionality fixes
"""

import requests
import json
import time

def test_model_status_endpoint():
    """Test the model-status endpoint for AIC/BIC values"""
    print("🧪 Testing Model Status Endpoint")
    print("=" * 50)
    
    api_url = "http://localhost:8001"
    
    try:
        # Test model status endpoint
        response = requests.get(f"{api_url}/model-status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Model status endpoint accessible")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            
            if data.get('model_loaded'):
                aic = data.get('aic')
                bic = data.get('bic')
                
                if aic is not None and bic is not None:
                    print(f"✅ AIC/BIC values found: AIC={aic}, BIC={bic}")
                    return True
                else:
                    print(f"⚠️ AIC/BIC values missing: AIC={aic}, BIC={bic}")
                    return False
            else:
                print("ℹ️ No model loaded yet - this is expected if no training has occurred")
                return True
        else:
            print(f"❌ API error: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
        print("💡 Make sure to start the server with: python arima_api.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_train_and_status():
    """Test training a model and checking status"""
    print("\n🎯 Testing Model Training and Status")
    print("=" * 50)
    
    api_url = "http://localhost:8001"
    
    try:
        # First configure prediction
        config_data = {
            "metric": "revenue",
            "period": "daily",
            "data_source": "sample_data",
            "sample_part": 1
        }
        
        print("📊 Configuring prediction...")
        response = requests.post(f"{api_url}/configure-prediction", 
                               json=config_data, timeout=30)
        
        if response.status_code == 200:
            print("✅ Configuration successful")
        else:
            print(f"⚠️ Configuration warning: {response.status_code}")
        
        # Train model
        print("🎯 Training model...")
        response = requests.post(f"{api_url}/train-model", timeout=60)
        
        if response.status_code == 200:
            print("✅ Model training successful")
            
            # Wait a moment for training to complete
            time.sleep(2)
            
            # Check model status
            print("📊 Checking model status...")
            response = requests.get(f"{api_url}/model-status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"📈 Model status: {json.dumps(data, indent=2)}")
                
                if data.get('model_loaded'):
                    aic = data.get('aic')
                    bic = data.get('bic')
                    model_params = data.get('model_params')
                    
                    print(f"✅ Model loaded successfully")
                    print(f"📊 ARIMA Order: {model_params}")
                    print(f"📈 AIC: {aic}")
                    print(f"📈 BIC: {bic}")
                    
                    if aic is not None and bic is not None:
                        print("✅ AIC/BIC values are properly available")
                        return True
                    else:
                        print("❌ AIC/BIC values are missing")
                        return False
                else:
                    print("❌ Model not loaded after training")
                    return False
            else:
                print(f"❌ Status check failed: {response.status_code}")
                return False
        else:
            print(f"❌ Training failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during training test: {e}")
        return False

def test_tomorrow_forecast():
    """Test tomorrow forecast generation"""
    print("\n🔮 Testing Tomorrow Forecast")
    print("=" * 50)
    
    api_url = "http://localhost:8001"
    
    try:
        # Generate tomorrow forecast
        forecast_data = {
            "horizon": "tomorrow"
        }
        
        print("🔮 Generating tomorrow forecast...")
        response = requests.post(f"{api_url}/generate-forecast", 
                               json=forecast_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Tomorrow forecast generated successfully")
            
            if data.get('success'):
                forecast = data.get('forecast', {})
                description = forecast.get('description', '')
                forecast_values = forecast.get('forecast_values', [])
                forecast_dates = forecast.get('forecast_dates', [])
                
                print(f"📊 Forecast description: {description}")
                print(f"📈 Forecast values: {len(forecast_values)} points")
                print(f"📅 Forecast dates: {len(forecast_dates)} dates")
                
                if 'tomorrow' in description.lower():
                    print("✅ Tomorrow forecast correctly identified")
                    print("💡 Frontend should display this as hourly chart")
                    return True
                else:
                    print(f"⚠️ Unexpected description: {description}")
                    return False
            else:
                print(f"❌ Forecast failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Forecast request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during forecast test: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 ARIMA FIXES TEST SUITE")
    print("=" * 60)
    print("Testing fixes for:")
    print("1. AIC/BIC display showing N/A")
    print("2. Chart not showing tomorrow predictions")
    print()
    
    results = []
    
    # Test 1: Model status endpoint
    results.append(test_model_status_endpoint())
    
    # Test 2: Training and status
    results.append(test_train_and_status())
    
    # Test 3: Tomorrow forecast
    results.append(test_tomorrow_forecast())
    
    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Fixes verified:")
        print("   • AIC/BIC values are properly extracted and displayed")
        print("   • Tomorrow forecasts are generated correctly")
        print("   • Frontend should now show hourly charts for tomorrow")
        print("\n💡 Next steps:")
        print("1. Start the web server: python arima_api.py")
        print("2. Open the web interface: http://localhost:3000")
        print("3. Train a model and verify AIC/BIC display")
        print("4. Generate a tomorrow forecast and check hourly chart")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure API server is running: python arima_api.py")
        print("2. Check sample data is available")
        print("3. Verify dependencies are installed")
    
    return passed == total

if __name__ == "__main__":
    main()
