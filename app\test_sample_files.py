#!/usr/bin/env python3
"""
Simple test script to verify sample files and create test data for the API
"""

import json
import os

def test_sample_files():
    """Test sample files directly without Flask dependencies"""
    print("🧪 Testing Sample Files")
    print("=" * 50)
    
    sample_dir = '../sample'
    
    if not os.path.exists(sample_dir):
        print(f"❌ Sample directory not found: {sample_dir}")
        return False
    
    available_parts = []
    
    for i in range(1, 11):
        file_path = f'{sample_dir}/part{i}.json'
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                part_info = {
                    'part_number': i,
                    'file_path': file_path,
                    'transaction_count': len(data.get('transactions', [])),
                    'metadata': data.get('metadata', {})
                }
                available_parts.append(part_info)
                
                metadata = part_info['metadata']
                time_period = metadata.get('time_period', {})
                month = time_period.get('month', 'Unknown')
                
                print(f"✅ Part {i}: {month} ({part_info['transaction_count']} transactions)")
                
            except Exception as e:
                print(f"❌ Error reading {file_path}: {e}")
        else:
            print(f"❌ Missing: {file_path}")
    
    print(f"\n📊 Total valid sample parts: {len(available_parts)}")
    
    # Create a test response that matches what the API should return
    api_response = {
        'success': True,
        'available_parts': available_parts,
        'total_parts': len(available_parts),
        'message': f'Found {len(available_parts)} sample parts available for testing'
    }
    
    # Save test response for debugging
    with open('test_api_response.json', 'w') as f:
        json.dump(api_response, f, indent=2)
    
    print(f"💾 Saved test API response to test_api_response.json")
    
    return len(available_parts) > 0

def create_sample_part_js():
    """Create a JavaScript file with sample parts data for testing"""
    print("\n🔧 Creating JavaScript test data...")
    
    sample_dir = '../sample'
    available_parts = []
    
    for i in range(1, 11):
        file_path = f'{sample_dir}/part{i}.json'
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                part_info = {
                    'part_number': i,
                    'file_path': file_path,
                    'transaction_count': len(data.get('transactions', [])),
                    'metadata': data.get('metadata', {})
                }
                available_parts.append(part_info)
                
            except Exception as e:
                print(f"❌ Error reading {file_path}: {e}")
    
    # Create JavaScript test data
    js_content = f"""
// Test data for sample parts (generated automatically)
const TEST_SAMPLE_PARTS = {json.dumps(available_parts, indent=2)};

// Function to simulate API response for testing
function getTestSampleParts() {{
    return {{
        success: true,
        available_parts: TEST_SAMPLE_PARTS,
        total_parts: {len(available_parts)},
        message: 'Found {len(available_parts)} sample parts available for testing'
    }};
}}

// Use this for testing when API is not available
if (typeof window !== 'undefined') {{
    window.TEST_SAMPLE_PARTS = TEST_SAMPLE_PARTS;
    window.getTestSampleParts = getTestSampleParts;
}}

console.log('📦 Test sample parts data loaded:', TEST_SAMPLE_PARTS.length, 'parts');
"""
    
    with open('test_sample_parts.js', 'w') as f:
        f.write(js_content)
    
    print(f"✅ Created test_sample_parts.js with {len(available_parts)} parts")
    return True

def main():
    """Run all tests"""
    print("🚀 SAMPLE FILES TEST SUITE")
    print("=" * 60)
    
    success = True
    
    if not test_sample_files():
        success = False
    
    if not create_sample_part_js():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("\n💡 Sample parts are ready for use!")
        print("\n📋 Available files:")
        print("- test_api_response.json: Expected API response")
        print("- test_sample_parts.js: JavaScript test data")
        print("\n🔧 To fix the loading issue:")
        print("1. Check that the API server is running")
        print("2. Verify the API endpoint is accessible")
        print("3. Check browser console for errors")
    else:
        print("❌ SOME TESTS FAILED!")
    
    return success

if __name__ == "__main__":
    main()
