<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reports ML Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Financial Reports ML Dashboard</h1>
                <div class="header-controls">
                    <div class="api-status" id="apiStatus">
                        <i class="fas fa-circle"></i>
                        <span>Checking API...</span>
                    </div>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Statistics Cards -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalRevenue">Loading...</h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalTransactions">Loading...</h3>
                            <p>Total Transactions</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="uniqueUsers">Loading...</h3>
                            <p>Unique Users</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="uniqueCountries">Loading...</h3>
                            <p>Countries</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Interactive Forecasting Section -->
            <section class="forecasting-section">
                <div class="forecasting-container">
                    <div class="forecasting-header">
                        <h2><i class="fas fa-crystal-ball"></i> Interactive ARIMA Forecasting</h2>
                        <div class="forecasting-subtitle">
                            <p>Choose what to predict and how far into the future</p>
                        </div>
                    </div>

                    <!-- Prediction Configuration -->
                    <div class="prediction-config">
                        <div class="config-grid">
                            <div class="config-group">
                                <label for="predictionMetric"><i class="fas fa-chart-bar"></i> What to Predict</label>
                                <select id="predictionMetric" onchange="updatePredictionOptions()">
                                    <option value="revenue">💰 Revenue (Financial Performance)</option>
                                    <option value="transaction_count">📊 Transaction Count (Customer Activity)</option>
                                    <option value="quantity">📦 Quantity (Demand Forecasting)</option>
                                </select>
                                <small class="config-help" id="metricHelp">Predict total revenue for financial planning and growth analysis</small>
                            </div>

                            <div class="config-group">
                                <label for="predictionPeriod"><i class="fas fa-calendar"></i> Time Period</label>
                                <select id="predictionPeriod" onchange="updatePredictionOptions()">
                                    <option value="daily">📅 Daily (High Detail)</option>
                                    <option value="weekly">📅 Weekly (Medium Detail)</option>
                                    <option value="monthly">📅 Monthly (Long-term Trends)</option>
                                </select>
                                <small class="config-help">Daily aggregation provides high granularity for short to medium-term forecasting</small>
                            </div>

                            <div class="config-group">
                                <label for="dataSource"><i class="fas fa-database"></i> Data Source</label>
                                <select id="dataSource" onchange="updatePredictionOptions()">
                                    <option value="full_dataset">📊 Full Dataset (~20K transactions)</option>
                                    <option value="sample_data">📦 Sample Data (1K transactions)</option>
                                </select>
                                <small class="config-help">Full dataset provides better accuracy for predictions</small>
                            </div>

                            <div class="config-group" id="samplePartGroup" style="display: none;">
                                <label for="samplePart"><i class="fas fa-puzzle-piece"></i> Sample Part</label>
                                <select id="samplePart" onchange="updatePredictionOptions()">
                                    <option value="">Loading sample parts...</option>
                                </select>
                                <small class="config-help">Each part contains data from different time periods for testing</small>
                            </div>
                        </div>
                    </div>

                    <!-- Forecast Horizon Selection -->
                    <div class="horizon-selection">
                        <div class="horizon-header">
                            <h3><i class="fas fa-clock"></i> Forecast Horizon</h3>
                            <div class="horizon-tabs">
                                <button class="tab-btn active" onclick="switchHorizonTab('quick')">Quick Select</button>
                                <button class="tab-btn" onclick="switchHorizonTab('custom')">Custom Period</button>
                                <button class="tab-btn" onclick="switchHorizonTab('natural')">Natural Language</button>
                            </div>
                        </div>

                        <!-- Quick Select Tab -->
                        <div class="horizon-content" id="quickHorizon">
                            <div class="quick-options">
                                <button class="horizon-btn" onclick="selectHorizon('tomorrow')">
                                    <i class="fas fa-sun"></i>
                                    <span>Tomorrow</span>
                                    <small>1 day</small>
                                </button>
                                <button class="horizon-btn" onclick="selectHorizon('next week')">
                                    <i class="fas fa-calendar-week"></i>
                                    <span>Next Week</span>
                                    <small>7 days</small>
                                </button>
                                <button class="horizon-btn" onclick="selectHorizon('next month')">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Next Month</span>
                                    <small>30 days</small>
                                </button>
                                <button class="horizon-btn" onclick="selectHorizon('next year')">
                                    <i class="fas fa-calendar"></i>
                                    <span>Next Year</span>
                                    <small>365 days</small>
                                </button>
                            </div>
                        </div>

                        <!-- Custom Period Tab -->
                        <div class="horizon-content hidden" id="customHorizon">
                            <div class="custom-controls">
                                <div class="input-group">
                                    <input type="number" id="customDays" min="1" max="1000" value="30" placeholder="Number">
                                    <select id="customUnit">
                                        <option value="days">Days</option>
                                        <option value="weeks">Weeks</option>
                                        <option value="months">Months</option>
                                        <option value="years">Years</option>
                                    </select>
                                    <button class="btn btn-secondary" onclick="selectCustomHorizon()">
                                        <i class="fas fa-check"></i> Select
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Natural Language Tab -->
                        <div class="horizon-content hidden" id="naturalHorizon">
                            <div class="natural-controls">
                                <div class="input-group">
                                    <input type="text" id="naturalInput" placeholder="e.g., '22 days from now', 'in the next 5 months', '3 weeks later'">
                                    <button class="btn btn-secondary" onclick="selectNaturalHorizon()">
                                        <i class="fas fa-magic"></i> Parse
                                    </button>
                                </div>
                                <div class="natural-examples">
                                    <small>Examples: "22 days from now", "next 3 months", "5 to 6 years from now"</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Forecast Info -->
                    <div class="forecast-summary">
                        <div class="summary-content">
                            <div class="summary-item">
                                <strong>Predicting:</strong> <span id="selectedMetric">Revenue</span>
                            </div>
                            <div class="summary-item">
                                <strong>Period:</strong> <span id="selectedPeriod">Daily</span>
                            </div>
                            <div class="summary-item">
                                <strong>Horizon:</strong> <span id="selectedHorizon">Select a time horizon</span>
                            </div>
                            <div class="summary-item">
                                <strong>Data:</strong> <span id="selectedData">Full Dataset</span>
                            </div>
                        </div>
                        <div class="summary-actions">
                            <button class="btn btn-primary" id="generateForecastBtn" onclick="generateSmartForecast()" disabled>
                                <i class="fas fa-rocket"></i> Generate Smart Forecast
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Charts Section -->
            <section class="charts-section">
                <div class="charts-grid">
                    <!-- Smart Forecast Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h2><i class="fas fa-brain"></i> Smart Forecast Results</h2>
                            <div class="chart-controls">
                                <div class="forecast-status" id="forecastStatus">
                                    <span>Configure and generate forecast above</span>
                                </div>
                            </div>
                        </div>
                        <div class="chart-content">
                            <canvas id="smartForecastChart"></canvas>
                            <div id="apiInstructions" class="api-instructions" style="display: none;">
                                <div class="instruction-card">
                                    <h3><i class="fas fa-rocket"></i> Start the API Server</h3>
                                    <p>To use the forecasting features, you need to start the API server:</p>
                                    <div class="instruction-steps">
                                        <div class="step">
                                            <span class="step-number">1</span>
                                            <span class="step-text">Open a terminal/command prompt</span>
                                        </div>
                                        <div class="step">
                                            <span class="step-number">2</span>
                                            <span class="step-text">Navigate to the app/ folder</span>
                                        </div>
                                        <div class="step">
                                            <span class="step-number">3</span>
                                            <span class="step-text">Run: <code>python basic_api.py</code></span>
                                        </div>
                                        <div class="step">
                                            <span class="step-number">4</span>
                                            <span class="step-text">Refresh this page</span>
                                        </div>
                                    </div>
                                    <div class="instruction-note">
                                        <i class="fas fa-info-circle"></i>
                                        The API server will run on port 8001 and provide all forecasting functionality.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-info" id="smartForecastInfo">
                            <div class="forecast-insights" id="forecastInsights">
                                <p>Generate a forecast to see business insights and recommendations</p>
                            </div>
                        </div>
                    </div>

                </div>
            </section>

            <!-- ARIMA Model Section -->
            <section class="model-section">
                <div class="model-container">
                    <div class="model-header">
                        <h2><i class="fas fa-brain"></i> ARIMA Model Status</h2>
                        <button class="btn btn-primary" onclick="trainModel()">
                            <i class="fas fa-cogs"></i> Train Model
                        </button>
                    </div>
                    <div class="model-content">
                        <div class="model-info" id="modelInfo">
                            <p>Loading model information...</p>
                        </div>
                        <div class="model-params" id="modelParams">
                            <!-- Model parameters will be displayed here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- API Testing Section -->
            <section class="api-section">
                <div class="api-container">
                    <div class="api-header">
                        <h2><i class="fas fa-code"></i> API Testing</h2>
                    </div>
                    <div class="api-content">
                        <div class="api-endpoints">
                            <div class="endpoint-group">
                                <h3>Available Endpoints</h3>
                                <div class="endpoint-list">
                                    <div class="endpoint-item">
                                        <span class="method get">GET</span>
                                        <span class="url">/health</span>
                                        <button class="btn btn-small" onclick="testEndpoint('/health')">Test</button>
                                    </div>
                                    <div class="endpoint-item">
                                        <span class="method get">GET</span>
                                        <span class="url">/database-stats</span>
                                        <button class="btn btn-small" onclick="testEndpoint('/database-stats')">Test</button>
                                    </div>
                                    <div class="endpoint-item">
                                        <span class="method get">GET</span>
                                        <span class="url">/model/info</span>
                                        <button class="btn btn-small" onclick="testEndpoint('/model/info')">Test</button>
                                    </div>
                                    <div class="endpoint-item">
                                        <span class="method get">GET</span>
                                        <span class="url">/forecast/quick</span>
                                        <button class="btn btn-small" onclick="testEndpoint('/forecast/quick?steps=7')">Test</button>
                                    </div>
                                </div>
                            </div>
                            <div class="api-response">
                                <h3>Response</h3>
                                <pre id="apiResponse">Click "Test" on any endpoint to see the response</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; RORRRRR</p>
                <div class="footer-links">
                    <a href="#" onclick="showDocumentation()">API Docs</a>
                    <a href="#" onclick="showAbout()">About</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Modal for notifications -->
    <div class="modal" id="notificationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Notification</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modalMessage">Message content</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">OK</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
