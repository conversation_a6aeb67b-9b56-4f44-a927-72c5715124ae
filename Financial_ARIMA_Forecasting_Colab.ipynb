{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🚀 Production-Grade Financial ARIMA Forecasting with PyTorch\n", "\n", "**Enterprise-Ready ARIMA Training Pipeline for Financial Transaction Data**\n", "\n", "This notebook provides a production-quality solution for training ARIMA models on financial transaction data using PyTorch with GPU acceleration.\n", "\n", "## 🎯 Production Features:\n", "- Mathematically correct PyTorch ARIMA implementation\n", "- Robust error handling and gradient management\n", "- Financial domain-specific data validation\n", "- Production-ready model architecture\n", "- Comprehensive performance evaluation\n", "- Business-ready forecast outputs\n", "\n", "## 📊 Data Requirements:\n", "Compatible with transaction data containing:\n", "- `transactiontime`: Transaction timestamps (datetime)\n", "- `total_transaction_value`: Revenue per transaction (float)\n", "- `numberofitemspurchased`: Items purchased (int)\n", "- `country`: Transaction location (string)\n", "- Additional transaction metadata\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🔧 1. Environment Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages for financial time series analysis\n", "!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install statsmodels scikit-learn plotly optuna tensorboard\n", "!pip install seaborn tqdm yfinance pandas-datareader\n", "\n", "print(\"✅ All packages installed successfully!\")\n", "print(\"🎯 Ready for financial time series forecasting!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Core libraries for production-grade financial forecasting\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.nn.utils import clip_grad_norm_\n", "\n", "# Data processing and analysis\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "from sklearn.preprocessing import RobustScaler\n", "from statsmodels.tsa.stattools import adfuller\n", "from statsmodels.tsa.seasonal import seasonal_decompose\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from scipy import stats\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import seaborn as sns\n", "\n", "# Utilities\n", "import warnings\n", "import logging\n", "import json\n", "import io\n", "import os\n", "from datetime import datetime, timedelta\n", "from tqdm.auto import tqdm\n", "from typing import Tuple, Dict, List, Optional, Union\n", "\n", "# Configure production environment\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('default')\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set random seeds for reproducibility\n", "torch.manual_seed(42)\n", "np.random.seed(42)\n", "\n", "# Device configuration with error handling\n", "try:\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    if torch.cuda.is_available():\n", "        torch.cuda.manual_seed(42)\n", "        torch.backends.cudnn.deterministic = True\n", "        torch.backends.cudnn.benchmark = False\n", "        \n", "    print(f\"🔥 PyTorch version: {torch.__version__}\")\n", "    print(f\"🚀 Using device: {device}\")\n", "    \n", "    if torch.cuda.is_available():\n", "        print(f\"💪 GPU: {torch.cuda.get_device_name()}\")\n", "        print(f\"📊 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "        \n", "except Exception as e:\n", "    logger.error(f\"Device configuration error: {e}\")\n", "    device = torch.device('cpu')\n", "    print(f\"⚠️ Falling back to CPU due to error: {e}\")\n", "\n", "print(\"\\n✅ Production environment ready for financial ARIMA forecasting!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_upload"}, "source": ["## 📁 2. Data Upload and Loading\n", "\n", "Upload your financial transaction CSV file or use the sample data generator."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["from google.colab import files\n", "\n", "# Option 1: Upload your transaction data CSV file\n", "print(\"📤 Upload your transaction data CSV file:\")\n", "print(\"Expected columns: transactiontime, total_transaction_value, numberofitemspurchased, country, etc.\")\n", "print(\"\\nClick 'Choose Files' to upload your data...\")\n", "\n", "uploaded = files.upload()\n", "\n", "# Load the uploaded file\n", "if uploaded:\n", "    filename = list(uploaded.keys())[0]\n", "    print(f\"\\n✅ Uploaded: {filename}\")\n", "    \n", "    # Read the CSV file\n", "    df = pd.read_csv(io.BytesIO(uploaded[filename]))\n", "    print(f\"📊 Loaded {len(df):,} transaction records\")\n", "    print(f\"📅 Columns: {list(df.columns)}\")\n", "    \n", "    # Display first few rows\n", "    print(\"\\n🔍 First 5 rows:\")\n", "    display(df.head())\n", "    \n", "    USE_UPLOADED_DATA = True\n", "else:\n", "    print(\"⚠️ No file uploaded. Will use sample data.\")\n", "    USE_UPLOADED_DATA = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_sample_data"}, "outputs": [], "source": ["# Option 2: Generate sample financial transaction data (if no file uploaded)\n", "if not USE_UPLOADED_DATA:\n", "    print(\"🎲 Generating realistic financial transaction sample data...\")\n", "    \n", "    # Set random seed for reproducibility\n", "    np.random.seed(42)\n", "    \n", "    # Generate date range (2 years of daily data)\n", "    start_date = '2022-01-01'\n", "    end_date = '2023-12-31'\n", "    dates = pd.date_range(start=start_date, end=end_date, freq='H')  # Hourly data\n", "    \n", "    n_records = len(dates)\n", "    \n", "    # Generate realistic transaction data\n", "    countries = ['United Kingdom', 'France', 'Germany', 'Netherlands', 'EIRE']\n", "    country_weights = [0.6, 0.15, 0.1, 0.1, 0.05]\n", "    \n", "    # Create transaction records\n", "    transactions = []\n", "    \n", "    for i, timestamp in enumerate(dates):\n", "        # Number of transactions per hour (varies by time of day and day of week)\n", "        hour = timestamp.hour\n", "        weekday = timestamp.weekday()\n", "        \n", "        # Business hours effect\n", "        if 9 <= hour <= 17:\n", "            base_transactions = np.random.poisson(8)\n", "        elif 18 <= hour <= 21:\n", "            base_transactions = np.random.poisson(5)\n", "        else:\n", "            base_transactions = np.random.poisson(2)\n", "        \n", "        # Weekend effect\n", "        if weekday >= 5:  # Weekend\n", "            base_transactions = int(base_transactions * 1.3)\n", "        \n", "        # Generate transactions for this hour\n", "        for _ in range(max(1, base_transactions)):\n", "            # Transaction value (log-normal distribution)\n", "            base_value = np.random.lognormal(mean=3.0, sigma=0.8)\n", "            \n", "            # Items purchased (Poisson distribution)\n", "            items = max(1, np.random.poisson(3))\n", "            \n", "            # Cost per item\n", "            cost_per_item = base_value / items\n", "            \n", "            # Total transaction value\n", "            total_value = items * cost_per_item\n", "            \n", "            # Country selection\n", "            country = np.random.choice(countries, p=country_weights)\n", "            \n", "            # Create transaction record\n", "            transaction = {\n", "                'userid': np.random.randint(100000, 999999),\n", "                'transactionid': np.random.randint(6000000, 7000000),\n", "                'transactiontime': timestamp,\n", "                'itemcode': np.random.randint(400000, 500000),\n", "                'itemdescription': f'PRODUCT_{np.random.randint(1, 1000)}',\n", "                'numberofitemspurchased': items,\n", "                'costperitem': round(cost_per_item, 2),\n", "                'country': country,\n", "                'total_transaction_value': round(total_value, 2),\n", "                'transaction_year': timestamp.year,\n", "                'transaction_month': timestamp.month,\n", "                'transaction_day': timestamp.day,\n", "                'transaction_weekday': timestamp.weekday(),\n", "                'transaction_hour': timestamp.hour\n", "            }\n", "            \n", "            transactions.append(transaction)\n", "    \n", "    # Create DataFrame\n", "    df = pd.DataFrame(transactions)\n", "    \n", "    print(f\"✅ Generated {len(df):,} sample transaction records\")\n", "    print(f\"📅 Date range: {df['transactiontime'].min()} to {df['transactiontime'].max()}\")\n", "    print(f\"💰 Total revenue: ${df['total_transaction_value'].sum():,.2f}\")\n", "    \n", "    # Display sample\n", "    print(\"\\n🔍 Sample data:\")\n", "    display(df.head())\n", "\n", "print(f\"\\n📊 Final dataset: {len(df):,} records ready for analysis\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_preprocessing"}, "source": ["## 🔄 3. Data Preprocessing and Time Series Creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "preprocess_data"}, "outputs": [], "source": ["# Convert transaction data to time series format\n", "print(\"🔄 Converting transaction data to time series...\")\n", "\n", "# Ensure transactiontime is datetime\n", "df['transactiontime'] = pd.to_datetime(df['transactiontime'])\n", "\n", "# Create different aggregation levels\n", "def create_time_series(data, freq='D', metrics=['revenue', 'transaction_count', 'avg_transaction_value', 'total_items']):\n", "    \"\"\"\n", "    Create time series from transaction data\n", "    freq: 'H' (hourly), 'D' (daily), 'W' (weekly), 'M' (monthly)\n", "    \"\"\"\n", "    # Group by time period\n", "    if freq == 'H':\n", "        grouped = data.groupby(data['transactiontime'].dt.floor('H'))\n", "    elif freq == 'D':\n", "        grouped = data.groupby(data['transactiontime'].dt.date)\n", "    elif freq == 'W':\n", "        grouped = data.groupby(pd.Grouper(key='transactiontime', freq='W'))\n", "    elif freq == 'M':\n", "        grouped = data.groupby(pd.Grouper(key='transactiontime', freq='M'))\n", "    \n", "    # Calculate metrics\n", "    agg_data = grouped.agg({\n", "        'total_transaction_value': ['sum', 'count', 'mean'],\n", "        'numberofitemspurchased': 'sum'\n", "    }).reset_index()\n", "    \n", "    # Flatten column names\n", "    agg_data.columns = ['date', 'revenue', 'transaction_count', 'avg_transaction_value', 'total_items']\n", "    \n", "    # Set date as index\n", "    agg_data['date'] = pd.to_datetime(agg_data['date'])\n", "    agg_data.set_index('date', inplace=True)\n", "    \n", "    # Sort by date\n", "    agg_data.sort_index(inplace=True)\n", "    \n", "    # Remove periods with zero transactions\n", "    agg_data = agg_data[agg_data['transaction_count'] > 0]\n", "    \n", "    return agg_data\n", "\n", "# Create daily time series (most common for ARIMA)\n", "daily_ts = create_time_series(df, freq='D')\n", "print(f\"📊 Daily time series: {len(daily_ts)} days\")\n", "print(f\"📅 Date range: {daily_ts.index.min()} to {daily_ts.index.max()}\")\n", "\n", "# Display summary statistics\n", "print(\"\\n📈 Daily Time Series Summary:\")\n", "display(daily_ts.describe())\n", "\n", "# Show first few rows\n", "print(\"\\n🔍 First 10 days:\")\n", "display(daily_ts.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data_visualization"}, "outputs": [], "source": ["# Create comprehensive data visualization\n", "print(\"📊 Creating comprehensive data visualizations...\")\n", "\n", "# Create subplots\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Daily Revenue', 'Daily Transaction Count', 'Average Transaction Value', 'Total Items Sold'),\n", "    vertical_spacing=0.1\n", ")\n", "\n", "# Daily Revenue\n", "fig.add_trace(\n", "    go.<PERSON>er(x=daily_ts.index, y=daily_ts['revenue'], \n", "               mode='lines', name='Revenue', line=dict(color='blue')),\n", "    row=1, col=1\n", ")\n", "\n", "# Daily Transaction Count\n", "fig.add_trace(\n", "    go.<PERSON>er(x=daily_ts.index, y=daily_ts['transaction_count'], \n", "               mode='lines', name='Transactions', line=dict(color='green')),\n", "    row=1, col=2\n", ")\n", "\n", "# Average Transaction Value\n", "fig.add_trace(\n", "    go.<PERSON>er(x=daily_ts.index, y=daily_ts['avg_transaction_value'], \n", "               mode='lines', name='Avg Value', line=dict(color='red')),\n", "    row=2, col=1\n", ")\n", "\n", "# Total Items\n", "fig.add_trace(\n", "    go.<PERSON>er(x=daily_ts.index, y=daily_ts['total_items'], \n", "               mode='lines', name='Items', line=dict(color='purple')),\n", "    row=2, col=2\n", ")\n", "\n", "fig.update_layout(\n", "    height=600,\n", "    title_text=\"Financial Time Series Overview\",\n", "    showlegend=False\n", ")\n", "\n", "fig.show()\n", "\n", "# Correlation analysis\n", "print(\"\\n🔗 Correlation Matrix:\")\n", "correlation_matrix = daily_ts.corr()\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('Time Series Metrics Correlation')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Data visualization complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "metric_selection"}, "source": ["## 🎯 4. Metric Selection and Time Series Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "select_metric"}, "outputs": [], "source": ["# Select the metric to forecast\n", "AVAILABLE_METRICS = ['revenue', 'transaction_count', 'avg_transaction_value', 'total_items']\n", "\n", "print(\"🎯 Available metrics for forecasting:\")\n", "for i, metric in enumerate(AVAILABLE_METRICS, 1):\n", "    print(f\"{i}. {metric}\")\n", "\n", "# For this demo, we'll use revenue (most common business metric)\n", "SELECTED_METRIC = 'revenue'\n", "print(f\"\\n📊 Selected metric: {SELECTED_METRIC}\")\n", "\n", "# Extract the time series for the selected metric\n", "time_series = daily_ts[SELECTED_METRIC].copy()\n", "\n", "print(f\"\\n📈 Time Series Statistics for {SELECTED_METRIC}:\")\n", "print(f\"Length: {len(time_series)} days\")\n", "print(f\"Mean: ${time_series.mean():,.2f}\")\n", "print(f\"Std: ${time_series.std():,.2f}\")\n", "print(f\"Min: ${time_series.min():,.2f}\")\n", "print(f\"Max: ${time_series.max():,.2f}\")\n", "\n", "# Create detailed visualization of selected metric\n", "fig = go.Figure()\n", "fig.add_trace(go.<PERSON>(\n", "    x=time_series.index,\n", "    y=time_series.values,\n", "    mode='lines',\n", "    name=f'Daily {SELECTED_METRIC.title()}',\n", "    line=dict(color='blue', width=2)\n", "))\n", "\n", "fig.update_layout(\n", "    title=f'Daily {SELECTED_METRIC.title()} Time Series',\n", "    xaxis_title='Date',\n", "    yaxis_title=f'{SELECTED_METRIC.title()}',\n", "    hovermode='x unified',\n", "    height=500\n", ")\n", "\n", "fig.show()\n", "\n", "print(\"✅ Metric selected and visualized!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "stationarity_analysis"}, "outputs": [], "source": ["# Stationarity analysis\n", "print(\"🔍 Analyzing time series stationarity...\")\n", "\n", "def check_stationarity(ts, title=\"Time Series\"):\n", "    \"\"\"\n", "    Check stationarity using Augmented Dickey-Fuller test\n", "    \"\"\"\n", "    # Perform ADF test\n", "    result = adfuller(ts.dropna())\n", "    \n", "    print(f\"\\n📊 Stationarity Test Results for {title}:\")\n", "    print(f\"ADF Statistic: {result[0]:.6f}\")\n", "    print(f\"p-value: {result[1]:.6f}\")\n", "    print(f\"Critical Values:\")\n", "    for key, value in result[4].items():\n", "        print(f\"\\t{key}: {value:.3f}\")\n", "    \n", "    if result[1] <= 0.05:\n", "        print(\"✅ Series is stationary (reject null hypothesis)\")\n", "        is_stationary = True\n", "    else:\n", "        print(\"❌ Series is non-stationary (fail to reject null hypothesis)\")\n", "        is_stationary = False\n", "    \n", "    return is_stationary, result\n", "\n", "# Test original series\n", "is_stationary, adf_result = check_stationarity(time_series, SELECTED_METRIC)\n", "\n", "# If not stationary, test first difference\n", "if not is_stationary:\n", "    print(\"\\n🔄 Testing first difference...\")\n", "    diff_series = time_series.diff().dropna()\n", "    is_diff_stationary, diff_adf_result = check_stationarity(diff_series, f\"First Difference of {SELECTED_METRIC}\")\n", "    \n", "    # If still not stationary, test second difference\n", "    if not is_diff_stationary:\n", "        print(\"\\n🔄 Testing second difference...\")\n", "        diff2_series = diff_series.diff().dropna()\n", "        is_diff2_stationary, diff2_adf_result = check_stationarity(diff2_series, f\"Second Difference of {SELECTED_METRIC}\")\n", "\n", "# Determine optimal differencing order\n", "if is_stationary:\n", "    optimal_d = 0\n", "    print(f\"\\n🎯 Recommended differencing order (d): {optimal_d}\")\n", "elif 'is_diff_stationary' in locals() and is_diff_stationary:\n", "    optimal_d = 1\n", "    print(f\"\\n🎯 Recommended differencing order (d): {optimal_d}\")\n", "else:\n", "    optimal_d = 2\n", "    print(f\"\\n🎯 Recommended differencing order (d): {optimal_d}\")\n", "\n", "print(\"✅ Stationarity analysis complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "decomposition_analysis"}, "outputs": [], "source": ["# Time series decomposition\n", "print(\"📊 Performing time series decomposition...\")\n", "\n", "# Determine period for decomposition\n", "if len(time_series) > 730:  # More than 2 years\n", "    period = 365  # Annual seasonality\n", "elif len(time_series) > 60:  # More than 2 months\n", "    period = 30   # Monthly seasonality\n", "else:\n", "    period = 7    # Weekly seasonality\n", "\n", "print(f\"Using period: {period} for decomposition\")\n", "\n", "# Perform decomposition\n", "try:\n", "    decomposition = seasonal_decompose(time_series.dropna(), model='additive', period=period)\n", "    \n", "    # Create decomposition plot\n", "    fig, axes = plt.subplots(4, 1, figsize=(15, 12))\n", "    \n", "    decomposition.observed.plot(ax=axes[0], title='Original Time Series')\n", "    decomposition.trend.plot(ax=axes[1], title='Trend Component')\n", "    decomposition.seasonal.plot(ax=axes[2], title='Seasonal Component')\n", "    decomposition.resid.plot(ax=axes[3], title='Residual Component')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Calculate component statistics\n", "    trend_strength = 1 - (decomposition.resid.var() / (decomposition.resid + decomposition.trend).var())\n", "    seasonal_strength = 1 - (decomposition.resid.var() / (decomposition.resid + decomposition.seasonal).var())\n", "    \n", "    print(f\"\\n📊 Decomposition Analysis:\")\n", "    print(f\"Trend Strength: {trend_strength:.3f}\")\n", "    print(f\"Seasonal Strength: {seasonal_strength:.3f}\")\n", "    \n", "    if trend_strength > 0.6:\n", "        print(\"✅ Strong trend component detected\")\n", "    if seasonal_strength > 0.6:\n", "        print(\"✅ Strong seasonal component detected\")\n", "        \n", "except Exception as e:\n", "    print(f\"⚠️ Decomposition failed: {e}\")\n", "    print(\"Continuing without decomposition...\")\n", "\n", "print(\"✅ Time series analysis complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "pytorch_arima_model"}, "source": ["## 🔥 5. PyTorch ARIMA Model Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pytorch_arima_class"}, "outputs": [], "source": ["class ProductionARIMA(nn.Module):\n", "    \"\"\"\n", "    Production-grade PyTorch ARIMA implementation for financial forecasting.\n", "    \n", "    Features:\n", "    - Mathematically correct ARIMA formulation\n", "    - Robust gradient flow and backpropagation\n", "    - Proper error term handling\n", "    - Financial domain constraints\n", "    \"\"\"\n", "    \n", "    def __init__(self, p: int, d: int, q: int, device: torch.device):\n", "        \"\"\"\n", "        Initialize ARIMA model.\n", "        \n", "        Args:\n", "            p: Autoregressive order\n", "            d: Differencing order\n", "            q: Moving average order\n", "            device: Torch device\n", "        \"\"\"\n", "        super(ProductionARIMA, self).__init__()\n", "        \n", "        # Validate parameters\n", "        if not all(isinstance(x, int) and x >= 0 for x in [p, d, q]):\n", "            raise ValueError(\"ARIMA orders must be non-negative integers\")\n", "        if p == 0 and q == 0:\n", "            raise ValueError(\"At least one of p or q must be positive\")\n", "        if d > 2:\n", "            logger.warning(\"Differencing order > 2 may lead to over-differencing\")\n", "            \n", "        self.p = p\n", "        self.d = d\n", "        self.q = q\n", "        self.device = device\n", "        \n", "        # Initialize parameters with proper constraints\n", "        if p > 0:\n", "            # AR parameters with stationarity constraints\n", "            self.ar_params = nn.Parameter(\n", "                torch.randn(p, device=device) * 0.1 / np.sqrt(p)\n", "            )\n", "        else:\n", "            self.register_parameter('ar_params', None)\n", "            \n", "        if q > 0:\n", "            # MA parameters with invertibility constraints\n", "            self.ma_params = nn.Parameter(\n", "                torch.randn(q, device=device) * 0.1 / np.sqrt(q)\n", "            )\n", "        else:\n", "            self.register_parameter('ma_params', None)\n", "            \n", "        # Intercept term\n", "        self.intercept = nn.Parameter(torch.zeros(1, device=device))\n", "        \n", "        # Innovation variance (log scale for positivity)\n", "        self.log_sigma = nn.Parameter(torch.zeros(1, device=device))\n", "        \n", "        # Buffer for error terms (not a parameter)\n", "        self.register_buffer('error_buffer', torch.zeros(1000, device=device))\n", "        self.error_idx = 0\n", "        \n", "    def apply_differencing(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Apply differencing to achieve stationarity.\n", "        \n", "        Args:\n", "            x: Input time series [batch_size, seq_len]\n", "            \n", "        Returns:\n", "            Differenced series\n", "        \"\"\"\n", "        if self.d == 0:\n", "            return x\n", "            \n", "        diff_x = x\n", "        for _ in range(self.d):\n", "            if diff_x.size(1) <= 1:\n", "                raise ValueError(\"Series too short for differencing order\")\n", "            diff_x = diff_x[:, 1:] - diff_x[:, :-1]\n", "            \n", "        return diff_x\n", "    \n", "    def enforce_stationarity(self) -> None:\n", "        \"\"\"\n", "        Enforce stationarity constraints on AR parameters.\n", "        \"\"\"\n", "        if self.ar_params is not None:\n", "            with torch.no_grad():\n", "                # Simple constraint: sum of AR coefficients < 1\n", "                ar_sum = torch.sum(torch.abs(self.ar_params))\n", "                if ar_sum >= 0.99:\n", "                    self.ar_params.data *= 0.99 / ar_sum\n", "    \n", "    def enforce_invertibility(self) -> None:\n", "        \"\"\"\n", "        Enforce invertibility constraints on MA parameters.\n", "        \"\"\"\n", "        if self.ma_params is not None:\n", "            with torch.no_grad():\n", "                # Simple constraint: sum of MA coefficients < 1\n", "                ma_sum = torch.sum(torch.abs(self.ma_params))\n", "                if ma_sum >= 0.99:\n", "                    self.ma_params.data *= 0.99 / ma_sum\n", "    \n", "    def forward(self, x: torch.Tensor, return_errors: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:\n", "        \"\"\"\n", "        Forward pass with proper ARIMA computation.\n", "        \n", "        Args:\n", "            x: Input time series [batch_size, seq_len]\n", "            return_errors: Whether to return error terms\n", "            \n", "        Returns:\n", "            Predictions and optionally error terms\n", "        \"\"\"\n", "        batch_size, seq_len = x.shape\n", "        \n", "        # Validate input\n", "        if torch.isnan(x).any() or torch.isinf(x).any():\n", "            raise ValueError(\"Input contains NaN or infinite values\")\n", "        \n", "        # Apply differencing\n", "        x_diff = self.apply_differencing(x)\n", "        diff_len = x_diff.size(1)\n", "        \n", "        if diff_len < max(self.p, self.q):\n", "            raise ValueError(f\"Sequence too short after differencing: {diff_len} < {max(self.p, self.q)}\")\n", "        \n", "        # Initialize outputs\n", "        predictions = torch.zeros_like(x_diff)\n", "        errors = torch.zeros_like(x_diff)\n", "        \n", "        # Enforce constraints\n", "        self.enforce_stationarity()\n", "        self.enforce_invertibility()\n", "        \n", "        # ARIMA computation\n", "        for t in range(max(self.p, self.q), diff_len):\n", "            # Intercept term\n", "            pred = self.intercept\n", "            \n", "            # AR component: φ₁x_{t-1} + φ₂x_{t-2} + ... + φₚx_{t-p}\n", "            if self.p > 0:\n", "                ar_terms = x_diff[:, t-self.p:t].flip(dims=[1])  # Reverse for proper indexing\n", "                ar_contribution = torch.sum(self.ar_params.unsqueeze(0) * ar_terms, dim=1)\n", "                pred = pred + ar_contribution\n", "            \n", "            # MA component: θ₁ε_{t-1} + θ₂ε_{t-2} + ... + θₑε_{t-q}\n", "            if self.q > 0 and t >= self.q:\n", "                ma_terms = errors[:, t-self.q:t].flip(dims=[1])  # Reverse for proper indexing\n", "                ma_contribution = torch.sum(self.ma_params.unsqueeze(0) * ma_terms, dim=1)\n", "                pred = pred + ma_contribution\n", "            \n", "            predictions[:, t] = pred\n", "            \n", "            # Compute error term: ε_t = x_t - ŷ_t\n", "            error = x_diff[:, t] - pred\n", "            errors[:, t] = error\n", "        \n", "        # Apply financial domain constraints (no negative revenues)\n", "        if return_errors:\n", "            return predictions, errors\n", "        return predictions\n", "    \n", "    def forecast(self, x: torch.Tensor, steps: int) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:\n", "        \"\"\"\n", "        Generate multi-step forecasts with confidence intervals.\n", "        \n", "        Args:\n", "            x: Historical time series\n", "            steps: Number of forecast steps\n", "            \n", "        Returns:\n", "            Forecasts and confidence intervals\n", "        \"\"\"\n", "        self.eval()\n", "        \n", "        with torch.no_grad():\n", "            # Get historical predictions and errors\n", "            _, hist_errors = self.forward(x, return_errors=True)\n", "            \n", "            # Apply differencing to get the differenced series\n", "            x_diff = self.apply_differencing(x)\n", "            \n", "            # Initialize forecast arrays\n", "            forecasts = torch.zeros(x.size(0), steps, device=self.device)\n", "            forecast_vars = torch.zeros(x.size(0), steps, device=self.device)\n", "            \n", "            # Extend series for forecasting\n", "            extended_series = torch.cat([x_diff, torch.zeros(x.size(0), steps, device=self.device)], dim=1)\n", "            extended_errors = torch.cat([hist_errors, torch.zeros(x.size(0), steps, device=self.device)], dim=1)\n", "            \n", "            sigma_sq = torch.exp(self.log_sigma) ** 2\n", "            \n", "            for h in range(steps):\n", "                t = x_diff.size(1) + h\n", "                \n", "                # Forecast mean\n", "                pred = self.intercept\n", "                \n", "                # AR component\n", "                if self.p > 0:\n", "                    ar_terms = extended_series[:, t-self.p:t].flip(dims=[1])\n", "                    pred = pred + torch.sum(self.ar_params.unsqueeze(0) * ar_terms, dim=1)\n", "                \n", "                # MA component (only for h < q)\n", "                if self.q > 0 and h < self.q:\n", "                    ma_terms = extended_errors[:, t-self.q:t].flip(dims=[1])\n", "                    pred = pred + torch.sum(self.ma_params.unsqueeze(0) * ma_terms, dim=1)\n", "                \n", "                forecasts[:, h] = pred\n", "                extended_series[:, t] = pred\n", "                \n", "                # Forecast variance (simplified)\n", "                forecast_vars[:, h] = sigma_sq * (h + 1)\n", "            \n", "            return forecasts, forecast_vars\n", "\n", "print(\"✅ Production-grade PyTorch ARIMA model defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "forecaster_class"}, "outputs": [], "source": ["# PyTorch ARIMA Forecaster Class\n", "class FinancialARIMAForecaster:\n", "    \"\"\"Complete ARIMA forecasting system for financial data\"\"\"\n", "    \n", "    def __init__(self, p=1, d=1, q=1, device=None):\n", "        if device is None:\n", "            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "        else:\n", "            self.device = device\n", "            \n", "        self.p = p\n", "        self.d = d\n", "        self.q = q\n", "        \n", "        self.model = None\n", "        self.optimizer = None\n", "        self.criterion = nn.MS<PERSON><PERSON>()\n", "        \n", "        self.training_history = []\n", "        self.original_data = None\n", "        self.scaled_data = None\n", "        self.scaler_params = None\n", "        \n", "        print(f\"🚀 Initialized ARIMA({p}, {d}, {q}) forecaster on {self.device}\")\n", "    \n", "    def prepare_data(self, data, train_ratio=0.8):\n", "        \"\"\"Prepare financial time series data for training\"\"\"\n", "        print(f\"📊 Preparing data: {len(data)} observations\")\n", "        \n", "        # Store original data\n", "        self.original_data = data.copy()\n", "        \n", "        # Convert to numpy and handle missing values\n", "        values = data.values.astype(np.float32)\n", "        values = np.nan_to_num(values, nan=np.nanmean(values))\n", "        \n", "        # Min-max scaling\n", "        self.scaler_params = {\n", "            'min': np.min(values),\n", "            'max': np.max(values)\n", "        }\n", "        \n", "        scaled_values = (values - self.scaler_params['min']) / (self.scaler_params['max'] - self.scaler_params['min'])\n", "        self.scaled_data = scaled_values\n", "        \n", "        # Split data\n", "        split_idx = int(len(scaled_values) * train_ratio)\n", "        train_data = scaled_values[:split_idx]\n", "        val_data = scaled_values[split_idx:]\n", "        \n", "        # Convert to tensors\n", "        train_tensor = torch.tensor(train_data, dtype=torch.float32, device=self.device).unsqueeze(0)\n", "        val_tensor = torch.tensor(val_data, dtype=torch.float32, device=self.device).unsqueeze(0)\n", "        \n", "        print(f\"✅ Data prepared: {len(train_data)} train, {len(val_data)} validation\")\n", "        \n", "        return train_tensor, val_tensor\n", "    \n", "    def create_model(self):\n", "        \"\"\"Create and initialize the PyTorch ARIMA model\"\"\"\n", "        self.model = PyTorchARIMA(self.p, self.d, self.q, self.device)\n", "        self.optimizer = optim.<PERSON>(self.model.parameters(), lr=0.01)\n", "        \n", "        # Count parameters\n", "        total_params = sum(p.numel() for p in self.model.parameters())\n", "        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)\n", "        \n", "        print(f\"🔧 Model created with {total_params} total parameters ({trainable_params} trainable)\")\n", "        \n", "        return self.model\n", "    \n", "    def train(self, train_data, val_data, epochs=1000, patience=100, verbose=True):\n", "        \"\"\"Train the ARIMA model\"\"\"\n", "        if self.model is None:\n", "            self.create_model()\n", "        \n", "        print(f\"🎯 Starting training for {epochs} epochs...\")\n", "        \n", "        best_val_loss = float('inf')\n", "        patience_counter = 0\n", "        train_losses = []\n", "        val_losses = []\n", "        \n", "        # Training loop with progress bar\n", "        pbar = tqdm(range(epochs), desc=\"Training ARIMA\")\n", "        \n", "        for epoch in pbar:\n", "            # Training step\n", "            self.model.train()\n", "            self.optimizer.zero_grad()\n", "            \n", "            train_pred = self.model(train_data)\n", "            train_loss = self.criterion(train_pred[:, max(self.p, self.q):], \n", "                                      train_data[:, max(self.p, self.q):])\n", "            \n", "            train_loss.backward()\n", "            self.optimizer.step()\n", "            \n", "            # Validation step\n", "            self.model.eval()\n", "            with torch.no_grad():\n", "                val_pred = self.model(val_data)\n", "                val_loss = self.criterion(val_pred[:, max(self.p, self.q):], \n", "                                        val_data[:, max(self.p, self.q):])\n", "            \n", "            train_losses.append(train_loss.item())\n", "            val_losses.append(val_loss.item())\n", "            \n", "            # Early stopping\n", "            if val_loss < best_val_loss:\n", "                best_val_loss = val_loss\n", "                patience_counter = 0\n", "                # Save best model state\n", "                torch.save(self.model.state_dict(), 'best_financial_arima.pth')\n", "            else:\n", "                patience_counter += 1\n", "            \n", "            # Update progress bar\n", "            pbar.set_postfix({\n", "                'Train Loss': f'{train_loss:.6f}',\n", "                'Val Loss': f'{val_loss:.6f}',\n", "                'Best': f'{best_val_loss:.6f}'\n", "            })\n", "            \n", "            if patience_counter >= patience:\n", "                print(f\"\\n⏰ Early stopping at epoch {epoch}\")\n", "                break\n", "        \n", "        # Load best model\n", "        self.model.load_state_dict(torch.load('best_financial_arima.pth'))\n", "        \n", "        self.training_history = {\n", "            'train_losses': train_losses,\n", "            'val_losses': val_losses,\n", "            'best_val_loss': best_val_loss.item(),\n", "            'epochs_trained': len(train_losses)\n", "        }\n", "        \n", "        print(f\"\\n✅ Training completed! Best validation loss: {best_val_loss:.6f}\")\n", "        \n", "        return self.training_history\n", "    \n", "    def forecast(self, steps=30):\n", "        \"\"\"Generate financial forecasts\"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"Model not trained. Call train() first.\")\n", "        \n", "        print(f\"🔮 Generating {steps}-step forecast...\")\n", "        \n", "        self.model.eval()\n", "        \n", "        # Use last part of scaled data as input\n", "        input_length = max(50, max(self.p, self.q) * 2)\n", "        last_data = self.scaled_data[-input_length:]\n", "        input_tensor = torch.tensor(last_data, dtype=torch.float32, device=self.device).unsqueeze(0)\n", "        \n", "        forecasts = []\n", "        current_input = input_tensor.clone()\n", "        \n", "        with torch.no_grad():\n", "            for _ in range(steps):\n", "                pred = self.model(current_input)\n", "                next_val = pred[:, -1].item()\n", "                \n", "                forecasts.append(next_val)\n", "                \n", "                # Update input for next prediction\n", "                next_tensor = torch.tensor([[next_val]], device=self.device)\n", "                current_input = torch.cat([current_input[:, 1:], next_tensor], dim=1)\n", "        \n", "        # Inverse transform forecasts\n", "        forecasts_array = np.array(forecasts)\n", "        forecasts_original = (forecasts_array * (self.scaler_params['max'] - self.scaler_params['min']) + \n", "                            self.scaler_params['min'])\n", "        \n", "        # Create forecast dates\n", "        last_date = self.original_data.index[-1]\n", "        forecast_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=steps, freq='D')\n", "        \n", "        forecast_df = pd.DataFrame({\n", "            'date': forecast_dates,\n", "            'forecast': forecasts_original\n", "        })\n", "        \n", "        print(f\"✅ Forecast generated for {steps} days\")\n", "        \n", "        return {\n", "            'forecast_df': forecast_df,\n", "            'forecast_values': forecasts_original,\n", "            'steps': steps\n", "        }\n", "    \n", "    def plot_training_history(self):\n", "        \"\"\"Plot training history\"\"\"\n", "        if not self.training_history:\n", "            print(\"⚠️ No training history available\")\n", "            return\n", "        \n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "        \n", "        # Full training history\n", "        ax1.plot(self.training_history['train_losses'], label='Training Loss', color='blue')\n", "        ax1.plot(self.training_history['val_losses'], label='Validation Loss', color='red')\n", "        ax1.set_xlabel('Epoch')\n", "        ax1.set_ylabel('Loss')\n", "        ax1.set_title('Training History')\n", "        ax1.legend()\n", "        ax1.set_yscale('log')\n", "        \n", "        # Last 100 epochs\n", "        last_n = min(100, len(self.training_history['train_losses']))\n", "        ax2.plot(self.training_history['train_losses'][-last_n:], label='Training Loss', color='blue')\n", "        ax2.plot(self.training_history['val_losses'][-last_n:], label='Validation Loss', color='red')\n", "        ax2.set_xlabel('Epoch')\n", "        ax2.set_ylabel('Loss')\n", "        ax2.set_title(f'Last {last_n} Epochs')\n", "        ax2.legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "print(\"✅ Financial ARIMA Forecaster class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "hyperparameter_optimization"}, "source": ["## 🔍 6. Hyperparameter Optimization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "optuna_optimization"}, "outputs": [], "source": ["# Install Optuna for hyperparameter optimization\n", "!pip install optuna\n", "\n", "import optuna\n", "from optuna.samplers import TPESampler\n", "\n", "def optimize_arima_parameters(time_series, n_trials=30):\n", "    \"\"\"Optimize ARIMA parameters using Optuna\"\"\"\n", "    \n", "    def objective(trial):\n", "        # Suggest hyperparameters\n", "        p = trial.suggest_int('p', 1, 5)\n", "        d = trial.suggest_int('d', 0, 2)\n", "        q = trial.suggest_int('q', 1, 5)\n", "        \n", "        try:\n", "            # Create and train model\n", "            forecaster = FinancialARIMAForecaster(p=p, d=d, q=q, device=device)\n", "            train_tensor, val_tensor = forecaster.prepare_data(time_series, train_ratio=0.8)\n", "            \n", "            # Quick training for optimization\n", "            history = forecaster.train(train_tensor, val_tensor, epochs=200, patience=30, verbose=False)\n", "            \n", "            return history['best_val_loss']\n", "            \n", "        except Exception as e:\n", "            print(f\"Trial failed: {e}\")\n", "            return float('inf')\n", "    \n", "    # Create study and optimize\n", "    study = optuna.create_study(direction='minimize', sampler=TPESampler())\n", "    \n", "    print(f\"🔍 Starting hyperparameter optimization with {n_trials} trials...\")\n", "    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)\n", "    \n", "    print(f\"\\n✅ Optimization completed!\")\n", "    print(f\"Best parameters: {study.best_params}\")\n", "    print(f\"Best validation loss: {study.best_value:.6f}\")\n", "    \n", "    return study.best_params, study.best_value\n", "\n", "# Run optimization\n", "print(\"🚀 Optimizing ARIMA parameters for financial forecasting...\")\n", "best_params, best_score = optimize_arima_parameters(time_series, n_trials=20)\n", "\n", "# Extract optimized parameters\n", "optimal_p = best_params['p']\n", "optimal_d = best_params['d'] \n", "optimal_q = best_params['q']\n", "\n", "print(f\"\\n🎯 Optimal ARIMA configuration: ({optimal_p}, {optimal_d}, {optimal_q})\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_training"}, "source": ["## 🎯 7. Final Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_final_model"}, "outputs": [], "source": ["# Create final model with optimized parameters\n", "print(f\"🚀 Training final ARIMA({optimal_p}, {optimal_d}, {optimal_q}) model...\")\n", "\n", "# Initialize forecaster with optimal parameters\n", "final_forecaster = FinancialARIMAForecaster(p=optimal_p, d=optimal_d, q=optimal_q, device=device)\n", "\n", "# Prepare data\n", "train_data, val_data = final_forecaster.prepare_data(time_series, train_ratio=0.8)\n", "\n", "print(f\"📊 Training data shape: {train_data.shape}\")\n", "print(f\"📊 Validation data shape: {val_data.shape}\")\n", "\n", "# Train the final model\n", "print(\"\\n🎯 Starting comprehensive training...\")\n", "training_history = final_forecaster.train(\n", "    train_data, \n", "    val_data, \n", "    epochs=1500,  # More epochs for final model\n", "    patience=150,  # More patience\n", "    verbose=True\n", ")\n", "\n", "print(f\"\\n✅ Final model training completed!\")\n", "print(f\"📊 Best validation loss: {training_history['best_val_loss']:.6f}\")\n", "print(f\"📊 Total epochs: {training_history['epochs_trained']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_training_results"}, "outputs": [], "source": ["# Plot training history\n", "print(\"📈 Visualizing training progress...\")\n", "final_forecaster.plot_training_history()\n", "\n", "# Additional training metrics\n", "train_losses = training_history['train_losses']\n", "val_losses = training_history['val_losses']\n", "\n", "print(f\"\\n📊 Training Summary:\")\n", "print(f\"Initial training loss: {train_losses[0]:.6f}\")\n", "print(f\"Final training loss: {train_losses[-1]:.6f}\")\n", "print(f\"Initial validation loss: {val_losses[0]:.6f}\")\n", "print(f\"Best validation loss: {training_history['best_val_loss']:.6f}\")\n", "print(f\"Loss reduction: {((val_losses[0] - training_history['best_val_loss']) / val_losses[0] * 100):.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "forecasting"}, "source": ["## 🔮 8. Generate Financial Forecasts"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_forecasts"}, "outputs": [], "source": ["# Generate forecasts\n", "FORECAST_DAYS = 30  # Forecast 30 days ahead\n", "\n", "print(f\"🔮 Generating {FORECAST_DAYS}-day financial forecast...\")\n", "forecast_results = final_forecaster.forecast(steps=FORECAST_DAYS)\n", "\n", "# Display forecast results\n", "forecast_df = forecast_results['forecast_df']\n", "print(f\"\\n📊 Forecast Summary:\")\n", "print(f\"Forecast period: {forecast_df['date'].min()} to {forecast_df['date'].max()}\")\n", "print(f\"Average daily {SELECTED_METRIC}: ${forecast_df['forecast'].mean():,.2f}\")\n", "print(f\"Total forecasted {SELECTED_METRIC}: ${forecast_df['forecast'].sum():,.2f}\")\n", "print(f\"Min forecasted {SELECTED_METRIC}: ${forecast_df['forecast'].min():,.2f}\")\n", "print(f\"Max forecasted {SELECTED_METRIC}: ${forecast_df['forecast'].max():,.2f}\")\n", "\n", "print(f\"\\n🔍 First 10 forecast days:\")\n", "display(forecast_df.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualize_forecasts"}, "outputs": [], "source": ["# Create comprehensive forecast visualization\n", "print(\"📊 Creating forecast visualization...\")\n", "\n", "# Prepare data for plotting\n", "historical_data = time_series.tail(90)  # Last 90 days of historical data\n", "forecast_data = forecast_results['forecast_df']\n", "\n", "# Create interactive plot\n", "fig = go.Figure()\n", "\n", "# Historical data\n", "fig.add_trace(go.<PERSON>(\n", "    x=historical_data.index,\n", "    y=historical_data.values,\n", "    mode='lines',\n", "    name='Historical Data',\n", "    line=dict(color='blue', width=2)\n", "))\n", "\n", "# Forecast data\n", "fig.add_trace(go.<PERSON>(\n", "    x=forecast_data['date'],\n", "    y=forecast_data['forecast'],\n", "    mode='lines+markers',\n", "    name='Forecast',\n", "    line=dict(color='red', width=2, dash='dash'),\n", "    marker=dict(size=4)\n", "))\n", "\n", "# Add vertical line to separate historical and forecast\n", "fig.add_vline(\n", "    x=historical_data.index[-1],\n", "    line_dash=\"dot\",\n", "    line_color=\"gray\",\n", "    annotation_text=\"Forecast Start\"\n", ")\n", "\n", "fig.update_layout(\n", "    title=f'Financial {SELECTED_METRIC.title()} Forecast - ARIMA({optimal_p}, {optimal_d}, {optimal_q})',\n", "    xaxis_title='Date',\n", "    yaxis_title=f'{SELECTED_METRIC.title()} ($)',\n", "    hovermode='x unified',\n", "    height=600,\n", "    template='plotly_white'\n", ")\n", "\n", "fig.show()\n", "\n", "# Create comparison with recent trends\n", "recent_avg = historical_data.tail(30).mean()\n", "forecast_avg = forecast_data['forecast'].mean()\n", "change_pct = ((forecast_avg - recent_avg) / recent_avg) * 100\n", "\n", "print(f\"\\n📈 Forecast vs Recent Trends:\")\n", "print(f\"Recent 30-day average: ${recent_avg:,.2f}\")\n", "print(f\"Forecast 30-day average: ${forecast_avg:,.2f}\")\n", "print(f\"Expected change: {change_pct:+.2f}%\")\n", "\n", "if change_pct > 5:\n", "    print(\"📈 Forecast indicates growth trend\")\n", "elif change_pct < -5:\n", "    print(\"📉 Forecast indicates declining trend\")\n", "else:\n", "    print(\"➡️ Forecast indicates stable trend\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_evaluation"}, "source": ["## 📊 9. Model Evaluation and Performance Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "evaluate_model"}, "outputs": [], "source": ["# Model evaluation on validation set\n", "print(\"📊 Evaluating model performance...\")\n", "\n", "# Generate predictions on validation set\n", "final_forecaster.model.eval()\n", "with torch.no_grad():\n", "    val_predictions = final_forecaster.model(val_data)\n", "\n", "# Convert to numpy for evaluation\n", "val_true = val_data.cpu().numpy().flatten()\n", "val_pred = val_predictions.cpu().numpy().flatten()\n", "\n", "# Ensure same length\n", "min_len = min(len(val_true), len(val_pred))\n", "val_true = val_true[:min_len]\n", "val_pred = val_pred[:min_len]\n", "\n", "# Calculate metrics\n", "mae = mean_absolute_error(val_true, val_pred)\n", "mse = mean_squared_error(val_true, val_pred)\n", "rmse = np.sqrt(mse)\n", "\n", "# MAPE (Mean Absolute Percentage Error)\n", "mape = np.mean(np.abs((val_true - val_pred) / (val_true + 1e-8))) * 100\n", "\n", "# R-squared\n", "ss_res = np.sum((val_true - val_pred) ** 2)\n", "ss_tot = np.sum((val_true - np.mean(val_true)) ** 2)\n", "r2 = 1 - (ss_res / ss_tot)\n", "\n", "print(f\"\\n📊 Model Performance Metrics:\")\n", "print(f\"Mean Absolute Error (MAE): {mae:.6f}\")\n", "print(f\"Mean Squared Error (MSE): {mse:.6f}\")\n", "print(f\"Root Mean Squared Error (RMSE): {rmse:.6f}\")\n", "print(f\"Mean Absolute Percentage Error (MAPE): {mape:.2f}%\")\n", "print(f\"R-squared (R²): {r2:.4f}\")\n", "\n", "# Performance interpretation\n", "if mape < 10:\n", "    print(\"✅ Excellent forecasting accuracy (MAPE < 10%)\")\n", "elif mape < 20:\n", "    print(\"✅ Good forecasting accuracy (MAPE < 20%)\")\n", "elif mape < 50:\n", "    print(\"⚠️ Reasonable forecasting accuracy (MAPE < 50%)\")\n", "else:\n", "    print(\"❌ Poor forecasting accuracy (MAPE > 50%)\")\n", "\n", "if r2 > 0.8:\n", "    print(\"✅ Strong model fit (R² > 0.8)\")\n", "elif r2 > 0.6:\n", "    print(\"✅ Good model fit (R² > 0.6)\")\n", "elif r2 > 0.4:\n", "    print(\"⚠️ Moderate model fit (R² > 0.4)\")\n", "else:\n", "    print(\"❌ Weak model fit (R² < 0.4)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "residual_analysis"}, "outputs": [], "source": ["# Residual analysis\n", "print(\"🔍 Performing residual analysis...\")\n", "\n", "residuals = val_true - val_pred\n", "\n", "# Create residual plots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Residuals over time\n", "axes[0, 0].plot(residuals)\n", "axes[0, 0].axhline(y=0, color='r', linestyle='--', alpha=0.7)\n", "axes[0, 0].set_title('Residuals Over Time')\n", "axes[0, 0].set_xlabel('Time')\n", "axes[0, 0].set_ylabel('Residuals')\n", "\n", "# Residuals histogram\n", "axes[0, 1].hist(residuals, bins=30, alpha=0.7, density=True)\n", "axes[0, 1].set_title('Residuals Distribution')\n", "axes[0, 1].set_xlabel('Residuals')\n", "axes[0, 1].set_ylabel('Density')\n", "\n", "# Q-Q plot\n", "from scipy import stats\n", "stats.probplot(residuals, dist=\"norm\", plot=axes[1, 0])\n", "axes[1, 0].set_title('Q-Q Plot')\n", "\n", "# Predicted vs Actual\n", "axes[1, 1].scatter(val_pred, val_true, alpha=0.6)\n", "axes[1, 1].plot([val_true.min(), val_true.max()], [val_true.min(), val_true.max()], 'r--', lw=2)\n", "axes[1, 1].set_xlabel('Predicted')\n", "axes[1, 1].set_ylabel('Actual')\n", "axes[1, 1].set_title('Predicted vs Actual')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Residual statistics\n", "print(f\"\\n📊 Residual Statistics:\")\n", "print(f\"Mean: {np.mean(residuals):.6f}\")\n", "print(f\"Std: {np.std(residuals):.6f}\")\n", "print(f\"Skewness: {stats.skew(residuals):.4f}\")\n", "print(f\"Kurtosis: {stats.kurtosis(residuals):.4f}\")\n", "\n", "# Normality test\n", "_, p_value = stats.normaltest(residuals)\n", "print(f\"Normality test p-value: {p_value:.6f}\")\n", "if p_value > 0.05:\n", "    print(\"✅ Residuals appear normally distributed\")\n", "else:\n", "    print(\"⚠️ Residuals may not be normally distributed\")"]}, {"cell_type": "markdown", "metadata": {"id": "save_results"}, "source": ["## 💾 10. Save Results and Export Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_model_and_results"}, "outputs": [], "source": ["# Save the trained model\n", "model_filename = f'financial_arima_{optimal_p}_{optimal_d}_{optimal_q}_model.pth'\n", "\n", "# Save complete model state\n", "model_data = {\n", "    'model_state_dict': final_forecaster.model.state_dict(),\n", "    'model_params': {'p': optimal_p, 'd': optimal_d, 'q': optimal_q},\n", "    'scaler_params': final_forecaster.scaler_params,\n", "    'training_history': training_history,\n", "    'performance_metrics': {\n", "        'mae': mae,\n", "        'mse': mse,\n", "        'rmse': rmse,\n", "        'mape': mape,\n", "        'r2': r2\n", "    }\n", "}\n", "\n", "torch.save(model_data, model_filename)\n", "print(f\"✅ Model saved as: {model_filename}\")\n", "\n", "# Save forecast results\n", "forecast_filename = f'financial_forecast_{datetime.now().strftime(\"%Y%m%d_%H%M\")}.csv'\n", "forecast_df.to_csv(forecast_filename, index=False)\n", "print(f\"✅ Forecast saved as: {forecast_filename}\")\n", "\n", "# Save comprehensive results\n", "results_summary = {\n", "    'model_info': {\n", "        'type': 'PyTorch ARIMA',\n", "        'parameters': f'ARIMA({optimal_p}, {optimal_d}, {optimal_q})',\n", "        'device': device,\n", "        'training_epochs': training_history['epochs_trained']\n", "    },\n", "    'data_info': {\n", "        'metric': SELECTED_METRIC,\n", "        'total_observations': len(time_series),\n", "        'training_observations': len(train_data.flatten()),\n", "        'validation_observations': len(val_data.flatten()),\n", "        'date_range': [str(time_series.index.min()), str(time_series.index.max())]\n", "    },\n", "    'performance': {\n", "        'mae': float(mae),\n", "        'mse': float(mse),\n", "        'rmse': float(rmse),\n", "        'mape': float(mape),\n", "        'r2': float(r2),\n", "        'best_validation_loss': training_history['best_val_loss']\n", "    },\n", "    'forecast_info': {\n", "        'forecast_days': FORECAST_DAYS,\n", "        'forecast_start': str(forecast_df['date'].min()),\n", "        'forecast_end': str(forecast_df['date'].max()),\n", "        'average_forecast': float(forecast_df['forecast'].mean()),\n", "        'total_forecast': float(forecast_df['forecast'].sum())\n", "    },\n", "    'optimization_info': {\n", "        'method': 'Optuna TPE Sampler',\n", "        'best_score': float(best_score),\n", "        'optimal_parameters': best_params\n", "    }\n", "}\n", "\n", "results_filename = f'financial_arima_results_{datetime.now().strftime(\"%Y%m%d_%H%M\")}.json'\n", "with open(results_filename, 'w') as f:\n", "    json.dump(results_summary, f, indent=2)\n", "\n", "print(f\"✅ Results summary saved as: {results_filename}\")\n", "\n", "# Download files\n", "print(\"\\n📥 Downloading files...\")\n", "files.download(model_filename)\n", "files.download(forecast_filename)\n", "files.download(results_filename)\n", "\n", "print(\"\\n✅ All files saved and downloaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "summary"}, "source": ["## 🎉 11. Summary and Business Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "final_summary"}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"🎯 FINANCIAL ARIMA FORECASTING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📊 MODEL CONFIGURATION:\")\n", "print(f\"Model Type: PyTorch ARIMA({optimal_p}, {optimal_d}, {optimal_q})\")\n", "print(f\"Training Device: {device}\")\n", "print(f\"Metric Forecasted: {SELECTED_METRIC}\")\n", "print(f\"Training Data: {len(time_series)} daily observations\")\n", "print(f\"Date Range: {time_series.index.min()} to {time_series.index.max()}\")\n", "\n", "print(f\"\\n🎯 MODEL PERFORMANCE:\")\n", "print(f\"Training Epochs: {training_history['epochs_trained']}\")\n", "print(f\"Best Validation Loss: {training_history['best_val_loss']:.6f}\")\n", "print(f\"Mean Absolute Percentage Error: {mape:.2f}%\")\n", "print(f\"R-squared Score: {r2:.4f}\")\n", "\n", "# Performance rating\n", "if mape < 10 and r2 > 0.8:\n", "    performance_rating = \"🌟 EXCELLENT\"\n", "elif mape < 20 and r2 > 0.6:\n", "    performance_rating = \"✅ GOOD\"\n", "elif mape < 50 and r2 > 0.4:\n", "    performance_rating = \"⚠️ FAIR\"\n", "else:\n", "    performance_rating = \"❌ POOR\"\n", "\n", "print(f\"Overall Performance: {performance_rating}\")\n", "\n", "print(f\"\\n🔮 FORECAST RESULTS:\")\n", "print(f\"Forecast Period: {FORECAST_DAYS} days\")\n", "print(f\"Forecast Dates: {forecast_df['date'].min()} to {forecast_df['date'].max()}\")\n", "print(f\"Average Daily {SELECTED_METRIC}: ${forecast_df['forecast'].mean():,.2f}\")\n", "print(f\"Total Forecasted {SELECTED_METRIC}: ${forecast_df['forecast'].sum():,.2f}\")\n", "\n", "# Trend analysis\n", "recent_avg = time_series.tail(30).mean()\n", "forecast_avg = forecast_df['forecast'].mean()\n", "trend_change = ((forecast_avg - recent_avg) / recent_avg) * 100\n", "\n", "if trend_change > 5:\n", "    trend_direction = \"📈 GROWTH\"\n", "elif trend_change < -5:\n", "    trend_direction = \"📉 DECLINE\"\n", "else:\n", "    trend_direction = \"➡️ STABLE\"\n", "\n", "print(f\"Trend vs Recent 30 days: {trend_change:+.2f}% ({trend_direction})\")\n", "\n", "print(f\"\\n💼 BUSINESS INSIGHTS:\")\n", "\n", "# Generate business insights based on forecast\n", "if SELECTED_METRIC == 'revenue':\n", "    monthly_forecast = forecast_df['forecast'].sum()\n", "    recent_monthly = time_series.tail(30).sum()\n", "    \n", "    print(f\"• Expected monthly revenue: ${monthly_forecast:,.2f}\")\n", "    print(f\"• Recent monthly revenue: ${recent_monthly:,.2f}\")\n", "    \n", "    if monthly_forecast > recent_monthly * 1.1:\n", "        print(f\"• 🚀 Strong growth expected - consider scaling operations\")\n", "    elif monthly_forecast < recent_monthly * 0.9:\n", "        print(f\"• ⚠️ Revenue decline expected - review business strategy\")\n", "    else:\n", "        print(f\"• ✅ Stable revenue expected - maintain current operations\")\n", "\n", "# Seasonal patterns\n", "if len(time_series) > 365:\n", "    print(f\"• 📅 Model captures long-term patterns and seasonality\")\n", "else:\n", "    print(f\"• 📅 Model based on {len(time_series)} days of data\")\n", "\n", "# Model reliability\n", "if mape < 15:\n", "    print(f\"• 🎯 High confidence in forecast accuracy\")\n", "elif mape < 30:\n", "    print(f\"• ⚠️ Moderate confidence - monitor actual vs predicted\")\n", "else:\n", "    print(f\"• ❌ Low confidence - consider additional data or features\")\n", "\n", "print(f\"\\n📁 DELIVERABLES:\")\n", "print(f\"• Trained PyTorch ARIMA model: {model_filename}\")\n", "print(f\"• 30-day forecast: {forecast_filename}\")\n", "print(f\"• Complete results: {results_filename}\")\n", "\n", "print(f\"\\n🚀 NEXT STEPS:\")\n", "print(f\"• Deploy model for real-time forecasting\")\n", "print(f\"• Integrate with existing API infrastructure\")\n", "print(f\"• Set up automated retraining pipeline\")\n", "print(f\"• Monitor forecast accuracy and model drift\")\n", "print(f\"• Consider ensemble methods for improved accuracy\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🎉 FINANCIAL ARIMA FORECASTING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 60)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}