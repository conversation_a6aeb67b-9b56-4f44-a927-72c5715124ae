# Complete Training Fix for ARIMA Model
# This fixes the training loop to show proper progress and handle dimensions correctly

import torch
import torch.nn as nn
from torch.nn.utils import clip_grad_norm_
import numpy as np
from tqdm.auto import tqdm
import time

def corrected_train_model(self, train_data, val_data, epochs=1000, patience=100, gradient_clip=1.0):
    """
    Corrected training method with proper progress display and dimension handling.
    """
    if self.model is None:
        self.create_model()
    
    print(f"🎯 Starting training for {epochs} epochs with patience {patience}")
    print(f"📊 Model parameters: {self.model.count_parameters()}")
    print(f"🔧 Device: {self.device}")
    print(f"📏 Train data shape: {train_data.shape}")
    print(f"📏 Val data shape: {val_data.shape}")
    
    # Training state
    best_val_loss = float('inf')
    patience_counter = 0
    train_losses = []
    val_losses = []
    learning_rates = []
    
    # Loss function
    criterion = nn.MSELoss()
    
    # Test forward pass first to catch dimension issues early
    print("\n🧪 Testing forward pass...")
    try:
        with torch.no_grad():
            self.model.eval()
            test_pred = self.model(train_data[:, :min(100, train_data.size(1))])
            print(f"✅ Forward pass test successful. Output shape: {test_pred.shape}")
            
            # Test dimension compatibility
            start_idx = max(self.p, self.q)
            if hasattr(self.model, 'apply_differencing'):
                test_diff = self.model.apply_differencing(train_data[:, :min(100, train_data.size(1))])
                test_target = test_diff[:, start_idx:]
            else:
                test_target = train_data[:, start_idx:min(100, train_data.size(1))]
            
            print(f"📏 Test prediction shape: {test_pred.shape}")
            print(f"📏 Test target shape: {test_target.shape}")
            
            if test_pred.size(1) != test_target.size(1):
                print(f"⚠️ Dimension mismatch detected, will be handled during training")
            else:
                print(f"✅ Dimensions compatible")
                
    except Exception as e:
        print(f"❌ Forward pass test failed: {e}")
        import traceback
        traceback.print_exc()
        raise RuntimeError(f"Model forward pass is broken: {e}")
    
    # Training loop with proper progress tracking
    print(f"\n🚀 Starting training loop...")
    start_time = time.time()
    
    try:
        # Use tqdm with explicit settings for Colab
        pbar = tqdm(range(epochs), desc="Training ARIMA", 
                   bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]')
        
        for epoch in pbar:
            epoch_start = time.time()
            
            # Training phase
            self.model.train()
            self.optimizer.zero_grad()
            
            try:
                # Forward pass
                train_pred = self.model(train_data)
                
                # Calculate loss with proper dimension handling
                start_idx = max(self.p, self.q)
                
                # Get target data (use differenced data if model applies differencing)
                if hasattr(self.model, 'apply_differencing'):
                    train_diff = self.model.apply_differencing(train_data)
                    target_data = train_diff[:, start_idx:]
                else:
                    target_data = train_data[:, start_idx:]
                
                # Ensure dimensions match
                if train_pred.size(1) != target_data.size(1):
                    min_len = min(train_pred.size(1), target_data.size(1))
                    train_pred = train_pred[:, :min_len]
                    target_data = target_data[:, :min_len]
                
                train_loss = criterion(train_pred, target_data)
                
                # Check for NaN/infinite loss
                if torch.isnan(train_loss) or torch.isinf(train_loss):
                    print(f"\n❌ Invalid loss at epoch {epoch}: {train_loss}")
                    break
                
                # Backward pass with gradient clipping
                train_loss.backward()
                grad_norm = clip_grad_norm_(self.model.parameters(), gradient_clip)
                self.optimizer.step()
                
            except Exception as e:
                print(f"\n❌ Training step failed at epoch {epoch}: {e}")
                import traceback
                traceback.print_exc()
                break
            
            # Validation phase
            self.model.eval()
            with torch.no_grad():
                try:
                    val_pred = self.model(val_data)
                    
                    # Get validation target
                    if hasattr(self.model, 'apply_differencing'):
                        val_diff = self.model.apply_differencing(val_data)
                        val_target = val_diff[:, start_idx:]
                    else:
                        val_target = val_data[:, start_idx:]
                    
                    # Ensure dimensions match for validation
                    if val_pred.size(1) != val_target.size(1):
                        min_len = min(val_pred.size(1), val_target.size(1))
                        val_pred = val_pred[:, :min_len]
                        val_target = val_target[:, :min_len]
                    
                    val_loss = criterion(val_pred, val_target)
                    
                    if torch.isnan(val_loss) or torch.isinf(val_loss):
                        print(f"\n❌ Invalid validation loss at epoch {epoch}: {val_loss}")
                        break
                        
                except Exception as e:
                    print(f"\n❌ Validation step failed at epoch {epoch}: {e}")
                    import traceback
                    traceback.print_exc()
                    break
            
            # Record metrics
            train_losses.append(train_loss.item())
            val_losses.append(val_loss.item())
            learning_rates.append(self.optimizer.param_groups[0]['lr'])
            
            # Early stopping and model checkpointing
            if val_loss < best_val_loss:
                best_val_loss = val_loss.item()
                patience_counter = 0
                
                # Save best model state
                self.best_model_state = {
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch,
                    'val_loss': best_val_loss
                }
            else:
                patience_counter += 1
            
            # Learning rate scheduling
            if hasattr(self, 'scheduler') and self.scheduler is not None:
                self.scheduler.step(val_loss)
            
            # Update progress bar
            pbar.set_postfix({
                'Train': f'{train_loss:.6f}',
                'Val': f'{val_loss:.6f}',
                'Best': f'{best_val_loss:.6f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.2e}',
                'Patience': f'{patience_counter}/{patience}'
            })
            
            # Print detailed progress every 50 epochs
            if (epoch + 1) % 50 == 0:
                elapsed = time.time() - start_time
                print(f"\nEpoch {epoch+1}/{epochs} - "
                      f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                      f"Best: {best_val_loss:.6f}, Time: {elapsed:.1f}s")
            
            # Early stopping
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping at epoch {epoch+1}")
                break
        
        # Load best model
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state['model_state_dict'])
            print(f"\n✅ Loaded best model from epoch {self.best_model_state['epoch']+1}")
        
        # Store training history
        self.training_history = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'learning_rates': learning_rates,
            'best_val_loss': best_val_loss,
            'epochs_trained': len(train_losses),
            'converged': patience_counter < patience,
            'total_time': time.time() - start_time
        }
        
        self.is_trained = True
        
        total_time = time.time() - start_time
        print(f"\n🎉 Training completed!")
        print(f"📊 Best validation loss: {best_val_loss:.6f}")
        print(f"📈 Epochs trained: {len(train_losses)}")
        print(f"⏱️ Total time: {total_time:.1f}s")
        print(f"🎯 Converged: {'Yes' if patience_counter < patience else 'No (early stopped)'}")
        
        return self.training_history
        
    except KeyboardInterrupt:
        print(f"\n⚠️ Training interrupted by user")
        return self.training_history if hasattr(self, 'training_history') else {}
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise RuntimeError(f"Training failed: {e}")

# Apply the corrected training method
print("🔧 Applying corrected training method...")
ProductionFinancialForecaster.train_model = corrected_train_model
print("✅ Corrected training method applied!")
print("📝 Now run your training cell again - you should see proper progress!")
