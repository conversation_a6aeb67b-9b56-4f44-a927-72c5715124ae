#!/usr/bin/env python3
"""
Test script for the Enhanced ARIMA Forecasting Web Application
"""

import os
import sys
import json

def test_dependencies():
    """Test if all required dependencies are available"""
    print("🔍 Testing dependencies...")
    
    required_packages = [
        'flask', 'flask_cors', 'pandas', 'numpy', 
        'statsmodels', 'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'flask_cors':
                __import__('flask_cors')
            elif package == 'sklearn':
                __import__('sklearn')
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available!")
    return True

def test_data_files():
    """Test if data files are available"""
    print("\n📊 Testing data files...")
    
    data_files = [
        '../cleaned_transaction_data.csv',
        '../sample/part1.json'
    ]
    
    available_files = []
    for file_path in data_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
            available_files.append(file_path)
        else:
            print(f"   ❌ {file_path}")
    
    if not available_files:
        print("❌ No data files found!")
        return False
    
    print(f"✅ Found {len(available_files)} data file(s)")
    return True

def test_arima_functionality():
    """Test basic ARIMA functionality"""
    print("\n🤖 Testing ARIMA functionality...")
    
    try:
        import pandas as pd
        import numpy as np
        from statsmodels.tsa.arima.model import ARIMA
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        values = np.random.randn(100).cumsum() + 100
        ts = pd.Series(values, index=dates)
        
        # Test ARIMA model
        model = ARIMA(ts, order=(1, 1, 1))
        fitted_model = model.fit()
        
        # Test forecast
        forecast = fitted_model.forecast(steps=7)
        
        print("   ✅ ARIMA model creation")
        print("   ✅ Model fitting")
        print("   ✅ Forecast generation")
        print("✅ ARIMA functionality working!")
        return True
        
    except Exception as e:
        print(f"   ❌ ARIMA test failed: {e}")
        return False

def test_api_syntax():
    """Test API file syntax"""
    print("\n🔧 Testing API syntax...")
    
    try:
        with open('arima_api.py', 'r') as f:
            code = f.read()
        
        # Try to compile the code
        compile(code, 'arima_api.py', 'exec')
        print("   ✅ API syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ Syntax error in API: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error reading API file: {e}")
        return False

def test_web_files():
    """Test web files exist and are valid"""
    print("\n🌐 Testing web files...")
    
    web_files = {
        'index.html': 'HTML',
        'styles.css': 'CSS', 
        'app.js': 'JavaScript',
        'server.py': 'Python'
    }
    
    all_good = True
    
    for filename, filetype in web_files.items():
        if os.path.exists(filename):
            print(f"   ✅ {filename} ({filetype})")
        else:
            print(f"   ❌ {filename} ({filetype}) - Missing!")
            all_good = False
    
    if all_good:
        print("✅ All web files present!")
    
    return all_good

def create_sample_data():
    """Create sample data for testing"""
    print("\n📦 Creating sample data for testing...")
    
    try:
        # Create sample transaction data
        sample_data = {
            "transactions": []
        }
        
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Generate 50 sample transactions
        start_date = datetime(2023, 1, 1)
        
        for i in range(50):
            transaction = {
                "transactiontime": (start_date + timedelta(days=i)).isoformat(),
                "total_transaction_value": round(np.random.uniform(10, 500), 2),
                "numberofitemspurchased": np.random.randint(1, 10),
                "userid": f"user{np.random.randint(1, 100)}",
                "country": np.random.choice(["UK", "US", "DE", "FR", "ES"])
            }
            sample_data["transactions"].append(transaction)
        
        # Create sample directory if it doesn't exist
        os.makedirs('../sample', exist_ok=True)
        
        # Save sample data
        with open('../sample/test_data.json', 'w') as f:
            json.dump(sample_data, f, indent=2)
        
        print("   ✅ Sample data created: ../sample/test_data.json")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create sample data: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 ENHANCED ARIMA FORECASTING APP - SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Data Files", test_data_files),
        ("ARIMA Functionality", test_arima_functionality),
        ("API Syntax", test_api_syntax),
        ("Web Files", test_web_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    # Try to create sample data if no data files found
    if not os.path.exists('../cleaned_transaction_data.csv') and not os.path.exists('../sample/part1.json'):
        print("\n📦 No data files found, creating sample data...")
        create_sample_data()
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("\n🚀 To start the application:")
        print("   1. Run: python arima_api.py (in one terminal)")
        print("   2. Run: python server.py (in another terminal)")
        print("   3. Open: http://localhost:3000")
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the app.")
        
        if not test_dependencies():
            print("\n💡 Install missing dependencies with:")
            print("   pip install flask flask-cors pandas numpy statsmodels scikit-learn")

if __name__ == "__main__":
    main()
