#!/usr/bin/env python3
"""
Test script to verify all 15 sample parts work correctly with the ARIMA model
"""

import sys
import os
import json
import requests
import time

def test_sample_parts_discovery():
    """Test that all 15 sample parts are discovered"""
    print("🔍 Testing Sample Parts Discovery")
    print("=" * 50)

    # Test file existence
    sample_dir = 'sample'
    found_parts = []

    for i in range(1, 16):
        file_path = f'{sample_dir}/part{i}.json'
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                metadata = data.get('metadata', {})
                time_period = metadata.get('time_period', {})
                month = time_period.get('month', 'Unknown')
                transaction_count = len(data.get('transactions', []))

                found_parts.append({
                    'part': i,
                    'month': month,
                    'transactions': transaction_count
                })

                print(f"✅ Part {i}: {month} ({transaction_count} transactions)")

            except Exception as e:
                print(f"❌ Error reading part {i}: {e}")
        else:
            print(f"❌ Missing part {i}: {file_path}")

    print(f"\n📊 Found {len(found_parts)} out of 15 sample parts")
    return len(found_parts) == 15

def test_api_sample_parts():
    """Test API endpoint for sample parts discovery"""
    print("\n🌐 Testing API Sample Parts Discovery")
    print("=" * 50)

    try:
        response = requests.get('http://localhost:8001/available-sample-parts', timeout=10)

        if response.status_code == 200:
            data = response.json()

            if data.get('success'):
                parts = data.get('available_parts', [])
                print(f"✅ API returned {len(parts)} sample parts")

                # Show first few and last few parts
                for i, part in enumerate(parts[:5]):
                    metadata = part.get('metadata', {})
                    time_period = metadata.get('time_period', {})
                    month = time_period.get('month', 'Unknown')
                    print(f"   📊 Part {part['part_number']}: {month} ({part['transaction_count']} transactions)")

                if len(parts) > 10:
                    print("   ...")
                    for part in parts[-3:]:
                        metadata = part.get('metadata', {})
                        time_period = metadata.get('time_period', {})
                        month = time_period.get('month', 'Unknown')
                        print(f"   📊 Part {part['part_number']}: {month} ({part['transaction_count']} transactions)")

                return len(parts) == 15
            else:
                print(f"❌ API error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ API returned status {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print("⚠️ API server not running - start with: python arima_api.py")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_sample_part_training():
    """Test training models with different sample parts"""
    print("\n🎯 Testing Sample Part Training")
    print("=" * 50)

    # Test a few representative sample parts
    test_parts = [1, 5, 11, 15]  # Mix of original and new parts

    for part_num in test_parts:
        print(f"\n📊 Testing Part {part_num}...")

        try:
            # Configure prediction
            config_data = {
                "metric": "revenue",
                "period": "daily",
                "data_source": "sample_data",
                "sample_part": part_num
            }

            response = requests.post('http://localhost:8001/configure-prediction',
                                   json=config_data, timeout=30)

            if response.status_code == 200:
                print(f"   ✅ Configuration successful for part {part_num}")

                # Train model
                response = requests.post('http://localhost:8001/train-model', timeout=60)

                if response.status_code == 200:
                    print(f"   ✅ Training successful for part {part_num}")

                    # Check model status
                    time.sleep(1)
                    response = requests.get('http://localhost:8001/model-status', timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if data.get('model_loaded'):
                            aic = data.get('aic')
                            bic = data.get('bic')
                            print(f"   📈 AIC: {aic}, BIC: {bic}")
                        else:
                            print(f"   ⚠️ Model not loaded after training")

                else:
                    print(f"   ❌ Training failed for part {part_num}: {response.status_code}")
                    return False
            else:
                print(f"   ❌ Configuration failed for part {part_num}: {response.status_code}")
                return False

        except Exception as e:
            print(f"   ❌ Error testing part {part_num}: {e}")
            return False

    print(f"\n✅ Successfully tested {len(test_parts)} sample parts")
    return True

def test_seasonal_patterns():
    """Test that different seasonal patterns are represented"""
    print("\n🎄 Testing Seasonal Pattern Coverage")
    print("=" * 50)

    seasonal_parts = {
        'Winter': [1, 2, 14],      # Feb 2019, Jan 2019, Feb 2018
        'Spring': [4, 5, 8, 13],   # May, April, March, March 2019
        'Summer': [6, 9, 12],      # July, June, August
        'Autumn': [3, 7],          # September, October
        'Holiday': [10, 11, 15]    # December, November, January
    }

    for season, parts in seasonal_parts.items():
        print(f"🌟 {season} Season:")
        for part in parts:
            file_path = f'sample/part{part}.json'
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    data = json.load(f)
                metadata = data.get('metadata', {})
                time_period = metadata.get('time_period', {})
                month = time_period.get('month', 'Unknown')
                print(f"   ✅ Part {part}: {month}")
            else:
                print(f"   ❌ Part {part}: Missing")

    return True

def main():
    """Run all tests for the 15 sample parts"""
    print("🚀 COMPREHENSIVE SAMPLE PARTS TEST SUITE")
    print("=" * 60)
    print("Testing all 15 sample parts (original 10 + new 5)")
    print()

    results = []

    # Test 1: File discovery
    results.append(test_sample_parts_discovery())

    # Test 2: API discovery
    results.append(test_api_sample_parts())

    # Test 3: Model training
    results.append(test_sample_part_training())

    # Test 4: Seasonal coverage
    results.append(test_seasonal_patterns())

    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)

    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Verification complete:")
        print("   • All 15 sample parts are available")
        print("   • API correctly discovers all parts")
        print("   • Model training works with new parts")
        print("   • Comprehensive seasonal coverage achieved")
        print("\n📊 Sample Parts Summary:")
        print("   • Parts 1-10: Original sample parts")
        print("   • Parts 11-15: Additional seasonal coverage")
        print("   • Total: 1,500 transactions across 15 time periods")
        print("   • Coverage: 2018-2019 with diverse seasonal patterns")
        print("\n🎯 Ready for comprehensive ARIMA testing!")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all sample files were generated correctly")
        print("2. Start API server: python arima_api.py")
        print("3. Check file permissions and JSON format")

    return passed == total

if __name__ == "__main__":
    main()
