#!/usr/bin/env python3
"""
Minimal ARIMA API - Basic functionality guaranteed to work
"""

try:
    from flask import Flask, request, jsonify
    from flask_cors import CORS
    import json
    import os
    from datetime import datetime
    
    app = Flask(__name__)
    CORS(app)
    
    print("✅ Flask and basic packages loaded successfully")
    
    # Simple in-memory storage
    app_data = {
        'model_trained': False,
        'current_config': {
            'metric': 'revenue',
            'period': 'daily',
            'data_source': 'full_dataset'
        },
        'sample_forecast': {
            'forecast_dates': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
            'forecast_values': [1000, 1050, 1100, 1075, 1125],
            'lower_ci': [900, 950, 1000, 975, 1025],
            'upper_ci': [1100, 1150, 1200, 1175, 1225]
        }
    }
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'message': 'Minimal API is running',
            'timestamp': datetime.now().isoformat()
        })
    
    @app.route('/prediction-options', methods=['GET'])
    def get_prediction_options():
        """Get available prediction options"""
        return jsonify({
            'metrics': {
                'revenue': {
                    'name': 'Revenue',
                    'description': 'Total transaction value (financial performance)',
                    'icon': '💰'
                },
                'transaction_count': {
                    'name': 'Transaction Count', 
                    'description': 'Number of transactions (customer activity)',
                    'icon': '📊'
                },
                'quantity': {
                    'name': 'Quantity',
                    'description': 'Total items purchased (demand forecasting)',
                    'icon': '📦'
                }
            },
            'periods': {
                'daily': {'name': 'Daily', 'description': 'High granularity'},
                'weekly': {'name': 'Weekly', 'description': 'Medium granularity'},
                'monthly': {'name': 'Monthly', 'description': 'Low granularity'}
            },
            'data_sources': {
                'full_dataset': {'name': 'Full Dataset', 'description': 'Complete data'},
                'sample_data': {'name': 'Sample Data', 'description': 'Sample data'}
            }
        })
    
    @app.route('/configure-prediction', methods=['POST'])
    def configure_prediction():
        """Configure prediction settings"""
        try:
            data = request.get_json()
            metric = data.get('metric', 'revenue')
            period = data.get('period', 'daily')
            data_source = data.get('data_source', 'full_dataset')
            
            # Update configuration
            app_data['current_config'] = {
                'metric': metric,
                'period': period,
                'data_source': data_source
            }
            app_data['model_trained'] = True
            
            print(f"Configuration updated: {metric}, {period}, {data_source}")
            
            return jsonify({
                'success': True,
                'message': 'Configuration updated successfully',
                'config': app_data['current_config'],
                'data_points': 100,  # Mock data
                'model_params': [1, 1, 1]  # Mock ARIMA params
            })
            
        except Exception as e:
            print(f"Configuration error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/generate-forecast', methods=['POST'])
    def generate_forecast():
        """Generate forecast"""
        try:
            data = request.get_json()
            user_input = data.get('horizon', 'next month')
            
            if not app_data['model_trained']:
                return jsonify({'success': False, 'error': 'No model trained'}), 400
            
            print(f"Generating forecast for: {user_input}")
            
            # Simple horizon parsing
            if 'tomorrow' in user_input.lower():
                steps = 1
                description = "Tomorrow"
            elif 'week' in user_input.lower():
                steps = 7
                description = "Next Week"
            elif 'month' in user_input.lower():
                steps = 30
                description = "Next Month"
            elif 'year' in user_input.lower():
                steps = 365
                description = "Next Year"
            else:
                steps = 30
                description = "Next 30 Days"
            
            # Generate mock forecast data
            import random
            base_value = 1000
            forecast_values = []
            forecast_dates = []
            lower_ci = []
            upper_ci = []
            
            for i in range(min(steps, 30)):  # Limit to 30 days for demo
                value = base_value + random.randint(-100, 100)
                forecast_values.append(value)
                forecast_dates.append(f"2024-01-{i+1:02d}")
                lower_ci.append(value - 50)
                upper_ci.append(value + 50)
            
            total_forecast = sum(forecast_values)
            average_forecast = total_forecast / len(forecast_values)
            growth_rate = random.uniform(-10, 15)  # Mock growth rate
            
            # Generate insights
            insights = []
            if growth_rate > 5:
                insights.append({
                    'type': 'positive',
                    'icon': '📈',
                    'title': 'Growth Expected',
                    'message': f'{growth_rate:.1f}% growth forecasted'
                })
            elif growth_rate < -5:
                insights.append({
                    'type': 'warning',
                    'icon': '📉',
                    'title': 'Decline Expected',
                    'message': f'{abs(growth_rate):.1f}% decline forecasted'
                })
            else:
                insights.append({
                    'type': 'neutral',
                    'icon': '📊',
                    'title': 'Stable Trend',
                    'message': 'Steady performance expected'
                })
            
            return jsonify({
                'success': True,
                'forecast': {
                    'user_input': user_input,
                    'description': description,
                    'steps': len(forecast_values),
                    'forecast_dates': forecast_dates,
                    'forecast_values': forecast_values,
                    'lower_ci': lower_ci,
                    'upper_ci': upper_ci,
                    'total_forecast': total_forecast,
                    'average_forecast': average_forecast,
                    'growth_rate': growth_rate,
                    'insights': insights,
                    'config': app_data['current_config']
                }
            })
            
        except Exception as e:
            print(f"Forecast error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/model-status', methods=['GET'])
    def get_model_status():
        """Get model status"""
        return jsonify({
            'model_loaded': app_data['model_trained'],
            'config': app_data['current_config'],
            'model_params': [1, 1, 1],
            'data_points': 100,
            'last_data_point': '2024-01-01'
        })
    
    @app.route('/database-stats', methods=['GET'])
    def get_database_stats():
        """Get database statistics"""
        return jsonify({
            'total_transactions': 20000,
            'total_revenue': 4237053.36,
            'unique_users': 1500,
            'unique_countries': 25
        })
    
    if __name__ == '__main__':
        print("🚀 Starting Minimal ARIMA API...")
        print("📊 This is a simplified version with mock data")
        print("🌐 API available at: http://localhost:8001")
        print("🛑 Press Ctrl+C to stop")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=8001, debug=False)

except ImportError as e:
    print(f"❌ Missing required package: {e}")
    print("Please install with: pip install flask flask-cors")
except Exception as e:
    print(f"❌ Error starting API: {e}")
    print("Please check your Python installation")
