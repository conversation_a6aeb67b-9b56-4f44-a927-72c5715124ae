<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Parts Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .config-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .config-help {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Sample Parts Test Page</h1>
        
        <div class="status info">
            <strong>Purpose:</strong> Test the sample parts loading functionality independently
        </div>

        <div class="config-group">
            <label for="dataSource">📊 Data Source</label>
            <select id="dataSource" onchange="updateDataSource()">
                <option value="full_dataset">📊 Full Dataset</option>
                <option value="sample_data">📦 Sample Data</option>
            </select>
            <small class="config-help">Select sample data to show the sample parts dropdown</small>
        </div>

        <div class="config-group" id="samplePartGroup" style="display: none;">
            <label for="samplePart">🧩 Sample Part</label>
            <select id="samplePart" onchange="updateSamplePart()">
                <option value="">Loading sample parts...</option>
            </select>
            <small class="config-help">Each part contains data from different time periods</small>
        </div>

        <div class="config-group">
            <button onclick="testApiEndpoint()">🔍 Test API Endpoint</button>
            <button onclick="loadFallbackParts()">🔄 Load Fallback Parts</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div id="status" class="status info">
            Ready to test sample parts functionality
        </div>

        <div class="log" id="log">
            <div>📋 Test log will appear here...</div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 Log cleared</div>';
        }

        function updateDataSource() {
            const dataSource = document.getElementById('dataSource').value;
            const samplePartGroup = document.getElementById('samplePartGroup');
            
            log(`📊 Data source changed to: ${dataSource}`);
            
            if (dataSource === 'sample_data') {
                samplePartGroup.style.display = 'block';
                log('✅ Sample part group shown');
                loadSampleParts();
            } else {
                samplePartGroup.style.display = 'none';
                log('❌ Sample part group hidden');
            }
        }

        function updateSamplePart() {
            const samplePart = document.getElementById('samplePart').value;
            log(`🧩 Sample part selected: ${samplePart || 'None'}`);
        }

        async function testApiEndpoint() {
            log('🔍 Testing API endpoint...');
            setStatus('Testing API endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/available-sample-parts`);
                log(`📡 Response status: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`📦 API Response: ${JSON.stringify(result, null, 2)}`);
                    
                    if (result.success && result.available_parts) {
                        updateSamplePartOptions(result.available_parts);
                        setStatus(`✅ API working! Found ${result.available_parts.length} parts`, 'success');
                    } else {
                        setStatus('⚠️ API returned no parts', 'error');
                    }
                } else {
                    log(`❌ API error: ${response.status} ${response.statusText}`);
                    setStatus(`❌ API error: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`);
                setStatus(`❌ Network error: ${error.message}`, 'error');
            }
        }

        function loadFallbackParts() {
            log('🔄 Loading fallback sample parts...');
            setStatus('Loading fallback parts...', 'info');
            
            const fallbackParts = [];
            const months = ['2019-02', '2019-01', '2018-09', '2018-05', '2018-04', 
                           '2018-07', '2018-10', '2018-03', '2018-06', '2018-12'];
            
            for (let i = 1; i <= 10; i++) {
                fallbackParts.push({
                    part_number: i,
                    transaction_count: 100,
                    metadata: {
                        time_period: {
                            month: months[i-1] || `2018-${String(i).padStart(2, '0')}`
                        }
                    }
                });
            }
            
            updateSamplePartOptions(fallbackParts);
            setStatus(`✅ Loaded ${fallbackParts.length} fallback parts`, 'success');
            log(`✅ Created ${fallbackParts.length} fallback parts`);
        }

        function updateSamplePartOptions(availableParts) {
            log(`🔧 Updating sample part options with ${availableParts.length} parts`);
            
            const samplePartSelect = document.getElementById('samplePart');
            
            if (!samplePartSelect) {
                log('❌ Sample part select element not found');
                return;
            }
            
            // Clear existing options
            samplePartSelect.innerHTML = '';
            
            if (!availableParts || availableParts.length === 0) {
                samplePartSelect.innerHTML = '<option value="">No sample parts available</option>';
                log('⚠️ No sample parts to display');
                return;
            }
            
            // Add default option
            samplePartSelect.innerHTML = '<option value="">Select a sample part...</option>';
            
            // Add available parts
            availableParts.forEach((part, index) => {
                const option = document.createElement('option');
                option.value = part.part_number;
                
                const metadata = part.metadata || {};
                const timePeriod = metadata.time_period || {};
                const month = timePeriod.month || 'Unknown';
                
                option.textContent = `Part ${part.part_number} - ${month} (${part.transaction_count} transactions)`;
                samplePartSelect.appendChild(option);
                
                log(`✅ Added option: ${option.textContent}`);
            });
            
            log(`✅ Successfully updated dropdown with ${availableParts.length} parts`);
        }

        async function loadSampleParts() {
            log('🔍 Loading sample parts...');
            
            try {
                await testApiEndpoint();
            } catch (error) {
                log(`❌ API failed, loading fallback: ${error.message}`);
                loadFallbackParts();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Test page loaded');
            setStatus('Ready to test sample parts', 'info');
        });
    </script>
</body>
</html>
