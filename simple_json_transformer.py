"""
Simple JSON Transformation Module for Financial Transaction Data
This module handles the transformation of cleaned CSV data to JSON formats
"""

import pandas as pd
import json
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_prepare_data(csv_file: str) -> pd.DataFrame:
    """Load and prepare data for JSON transformation"""
    logger.info(f"Loading data from {csv_file}")
    
    df = pd.read_csv(csv_file)
    
    # Convert transactiontime to datetime
    if 'transactiontime' in df.columns:
        df['transactiontime'] = pd.to_datetime(df['transactiontime'], errors='coerce')
    
    logger.info(f"Data loaded. Shape: {df.shape}")
    return df

def create_records_json(df: pd.DataFrame, output_file: str = 'transaction_records.json'):
    """Create records format JSON"""
    logger.info("Creating records JSON...")
    
    # Prepare data for JSON
    df_json = df.copy()
    
    # Convert datetime to string
    if 'transactiontime' in df_json.columns:
        df_json['transactiontime'] = df_json['transactiontime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Round numerical values
    numerical_columns = ['costperitem', 'total_transaction_value']
    for col in numerical_columns:
        if col in df_json.columns:
            df_json[col] = df_json[col].round(2)
    
    # Convert to records
    records = df_json.to_dict('records')
    
    # Create metadata
    metadata = {
        'total_records': len(records),
        'columns': list(df_json.columns),
        'transformation_date': datetime.now().isoformat(),
        'source_file': 'cleaned_transaction_data.csv'
    }
    
    # Create final JSON structure
    json_output = {
        'metadata': metadata,
        'transactions': records
    }
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_output, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Records JSON saved to {output_file}")
    return json_output

def create_aggregated_json(df: pd.DataFrame, output_file: str = 'transaction_aggregated.json'):
    """Create aggregated format JSON"""
    logger.info("Creating aggregated JSON...")
    
    # Overall statistics
    overall_stats = {
        'total_transactions': int(len(df)),
        'unique_users': int(df['userid'].nunique()),
        'unique_countries': int(df['country'].nunique()),
        'unique_products': int(df['itemdescription'].nunique()),
        'total_revenue': float(df['total_transaction_value'].sum().round(2)),
        'average_transaction_value': float(df['total_transaction_value'].mean().round(2)),
        'date_range': {
            'start': df['transactiontime'].min().strftime('%Y-%m-%d'),
            'end': df['transactiontime'].max().strftime('%Y-%m-%d')
        }
    }
    
    # Country-wise aggregation
    country_stats = df.groupby('country').agg({
        'userid': 'nunique',
        'transactionid': 'count',
        'total_transaction_value': ['sum', 'mean'],
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    # Flatten column names for country stats
    country_stats.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] for col in country_stats.columns]
    country_aggregated = country_stats.to_dict('index')
    
    # Monthly aggregation
    df['year_month'] = df['transactiontime'].dt.to_period('M').astype(str)
    monthly_stats = df.groupby('year_month').agg({
        'userid': 'nunique',
        'transactionid': 'count',
        'total_transaction_value': ['sum', 'mean'],
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    # Flatten column names for monthly stats
    monthly_stats.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] for col in monthly_stats.columns]
    monthly_aggregated = monthly_stats.to_dict('index')
    
    # Top products
    top_products = df.groupby('itemdescription').agg({
        'transactionid': 'count',
        'total_transaction_value': 'sum',
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    top_products.columns = ['transaction_count', 'total_revenue', 'total_quantity']
    top_products = top_products.sort_values('total_revenue', ascending=False).head(20)
    top_products_dict = top_products.to_dict('index')
    
    # Create final aggregated JSON structure
    aggregated_output = {
        'metadata': {
            'aggregation_date': datetime.now().isoformat(),
            'source_file': 'cleaned_transaction_data.csv',
            'total_records_processed': len(df)
        },
        'overall_statistics': overall_stats,
        'country_analysis': country_aggregated,
        'monthly_trends': monthly_aggregated,
        'top_products': top_products_dict
    }
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(aggregated_output, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Aggregated JSON saved to {output_file}")
    return aggregated_output

def create_timeseries_json(df: pd.DataFrame, output_file: str = 'transaction_timeseries.json'):
    """Create time series format JSON for ARIMA modeling"""
    logger.info("Creating time series JSON...")
    
    # Daily time series
    daily_series = df.groupby(df['transactiontime'].dt.date).agg({
        'total_transaction_value': 'sum',
        'transactionid': 'count',
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    daily_series.index = daily_series.index.astype(str)
    daily_series.columns = ['daily_revenue', 'daily_transactions', 'daily_quantity']
    
    # Weekly time series
    weekly_series = df.groupby(df['transactiontime'].dt.to_period('W')).agg({
        'total_transaction_value': 'sum',
        'transactionid': 'count',
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    weekly_series.index = weekly_series.index.astype(str)
    weekly_series.columns = ['weekly_revenue', 'weekly_transactions', 'weekly_quantity']
    
    # Monthly time series
    monthly_series = df.groupby(df['transactiontime'].dt.to_period('M')).agg({
        'total_transaction_value': 'sum',
        'transactionid': 'count',
        'numberofitemspurchased': 'sum'
    }).round(2)
    
    monthly_series.index = monthly_series.index.astype(str)
    monthly_series.columns = ['monthly_revenue', 'monthly_transactions', 'monthly_quantity']
    
    # Create time series JSON structure
    timeseries_output = {
        'metadata': {
            'transformation_date': datetime.now().isoformat(),
            'source_file': 'cleaned_transaction_data.csv',
            'purpose': 'Time series data for ARIMA forecasting'
        },
        'daily_data': daily_series.to_dict('index'),
        'weekly_data': weekly_series.to_dict('index'),
        'monthly_data': monthly_series.to_dict('index')
    }
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(timeseries_output, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Time series JSON saved to {output_file}")
    return timeseries_output

def main():
    """Main function to run JSON transformation"""
    
    # Check if cleaned data exists
    cleaned_file = 'cleaned_transaction_data.csv'
    if not os.path.exists(cleaned_file):
        logger.error(f"Cleaned data file {cleaned_file} not found. Please run data cleaning first.")
        return
    
    try:
        # Load data
        df = load_and_prepare_data(cleaned_file)
        
        # Create all JSON formats
        logger.info("Starting JSON transformations...")
        
        create_records_json(df)
        create_aggregated_json(df)
        create_timeseries_json(df)
        
        # Print summary
        print("\n" + "="*60)
        print("JSON TRANSFORMATION COMPLETED")
        print("="*60)
        
        output_files = [
            'transaction_records.json',
            'transaction_aggregated.json', 
            'transaction_timeseries.json'
        ]
        
        print("Generated files:")
        for filename in output_files:
            if os.path.exists(filename):
                file_size = os.path.getsize(filename) / 1024  # KB
                print(f"  {filename} ({file_size:.1f} KB)")
        
        print("\nJSON files are ready for database loading and API development!")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
