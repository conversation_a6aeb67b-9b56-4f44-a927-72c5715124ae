/* Financial Reports ML Dashboard Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header h1 i {
    margin-right: 0.5rem;
    color: #fbbf24;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.api-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    font-size: 0.9rem;
}

.api-status.online i {
    color: var(--success-color);
}

.api-status.offline i {
    color: var(--error-color);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Charts Section */
.charts-section {
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--surface-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.chart-container.full-width {
    grid-column: 1 / -1;
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-header h2 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-content {
    padding: 1.5rem;
    height: 400px;
    position: relative;
}

.chart-info {
    padding: 1rem 1.5rem;
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Model Section */
.model-section {
    margin-bottom: 2rem;
}

.model-container {
    background: var(--surface-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.model-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.model-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.model-header h2 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.model-content {
    padding: 1.5rem;
}

.model-info {
    margin-bottom: 1rem;
}

.model-params {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.param-item {
    padding: 1rem;
    background: var(--background-color);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

.param-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.param-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* API Section */
.api-section {
    margin-bottom: 2rem;
}

.api-container {
    background: var(--surface-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.api-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.api-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.api-header h2 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.api-content {
    padding: 1.5rem;
}

.api-endpoints {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.endpoint-group h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.endpoint-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.endpoint-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--background-color);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

.method {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.method.get {
    background: var(--success-color);
    color: white;
}

.method.post {
    background: var(--primary-color);
    color: white;
}

.url {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.api-response h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.api-response pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Form Elements */
select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.9rem;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: 0.75rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--surface-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

/* Footer */
.footer {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 1rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Interactive Forecasting Section */
.forecasting-section {
    margin-bottom: 2rem;
    background: var(--surface-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.forecasting-container {
    max-width: 100%;
}

.forecasting-header {
    text-align: center;
    margin-bottom: 2rem;
}

.forecasting-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.forecasting-subtitle p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Prediction Configuration */
.prediction-config {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--background-color);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.config-group label {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.config-group select {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.config-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.config-help {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Horizon Selection */
.horizon-selection {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--background-color);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.horizon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.horizon-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.horizon-tabs {
    display: flex;
    gap: 0.25rem;
    background: var(--surface-color);
    padding: 0.25rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.tab-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.tab-btn:hover:not(.active) {
    background: var(--border-color);
    color: var(--text-primary);
}

.horizon-content {
    transition: opacity 0.3s ease;
}

.horizon-content.hidden {
    display: none;
}

/* Quick Options */
.quick-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.horizon-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--surface-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.horizon-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.horizon-btn i {
    font-size: 1.5rem;
}

.horizon-btn span {
    font-weight: 600;
    font-size: 0.95rem;
}

.horizon-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Custom and Natural Controls */
.custom-controls, .natural-controls {
    display: flex;
    justify-content: center;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.input-group input, .input-group select {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.95rem;
}

.input-group input:focus, .input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.natural-examples {
    margin-top: 0.75rem;
    text-align: center;
}

/* Forecast Summary */
.forecast-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    color: white;
    flex-wrap: wrap;
    gap: 1rem;
}

.summary-content {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.summary-item strong {
    font-size: 0.8rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-item span {
    font-size: 1rem;
    font-weight: 600;
}

.summary-actions {
    display: flex;
    gap: 0.5rem;
}

#generateForecastBtn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#generateForecastBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#generateForecastBtn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.forecast-status {
    padding: 0.5rem 1rem;
    background: var(--background-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.forecast-insights {
    display: grid;
    gap: 1rem;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--surface-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.insight-icon {
    font-size: 1.25rem;
    width: 2rem;
    text-align: center;
}

.insight-content {
    flex: 1;
}

.insight-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.insight-value {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

/* API Instructions */
.api-instructions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}

.instruction-card {
    background: var(--surface-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    max-width: 500px;
    text-align: center;
}

.instruction-card h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.instruction-steps {
    margin: 1.5rem 0;
    text-align: left;
}

.step {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--background-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.step-text {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.step-text code {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.instruction-note {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.instruction-note i {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .main-content {
        padding: 1rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-content {
        height: 300px;
    }

    .api-endpoints {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .forecasting-section {
        padding: 1rem;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .quick-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .horizon-header {
        flex-direction: column;
        align-items: stretch;
    }

    .forecast-summary {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .summary-content {
        justify-content: center;
    }
}
