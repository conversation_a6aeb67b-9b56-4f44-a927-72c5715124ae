"""
Simple test for Enhanced ARIMA Forecasting System
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime

def generate_simple_test_data(n_points=100):
    """Generate simple test data"""
    dates = pd.date_range(start='2023-01-01', periods=n_points, freq='D')
    
    # Simple trend + noise
    trend = np.linspace(1000, 1200, n_points)
    noise = np.random.normal(0, 20, n_points)
    values = trend + noise
    
    return pd.Series(values, index=dates, name='revenue')

def test_enhanced_arima():
    """Test the enhanced ARIMA system"""
    print("🧪 Simple Enhanced ARIMA Test")
    print("=" * 40)
    
    try:
        # Import the enhanced forecaster
        from enhanced_arima_forecasting import EnhancedARIMAForecaster
        
        print("✅ Enhanced ARIMA module imported successfully")
        
        # Generate test data
        print("\n📊 Generating test data...")
        test_data = generate_simple_test_data(80)
        print(f"✅ Generated {len(test_data)} data points")
        
        # Initialize forecaster
        print("\n🚀 Initializing Enhanced ARIMA Forecaster...")
        forecaster = EnhancedARIMAForecaster(
            cache_size=50,
            enable_ensemble=True,
            enable_seasonal=False,  # Disable for simple test
            n_jobs=1  # Single core for stability
        )
        print("✅ Forecaster initialized")
        
        # Set up data manually (bypass file loading for test)
        print("\n📈 Setting up time series data...")
        forecaster.time_series = test_data
        forecaster.df = test_data.to_frame('revenue')
        forecaster.last_date = test_data.index.max()
        print("✅ Data setup complete")
        
        # Test parameter optimization
        print("\n🔍 Testing parameter optimization...")
        start_time = time.time()
        
        optimal_params = forecaster.find_optimal_parameters_advanced(max_p=3, max_q=3, use_bayesian=False)
        
        param_time = time.time() - start_time
        print(f"✅ Found {len(optimal_params)} parameter sets in {param_time:.2f}s")
        print(f"   Parameters: {optimal_params}")
        
        # Test model training
        print("\n🤖 Testing model training...")
        start_time = time.time()
        
        success = forecaster.train_ensemble_models(optimal_params[:3])  # Use top 3
        
        train_time = time.time() - start_time
        
        if success:
            print(f"✅ Trained {len(forecaster.fitted_models)} models in {train_time:.2f}s")
            
            # Show model details
            for model_id, params in forecaster.model_params.items():
                aic = forecaster.fitted_models[model_id].aic
                weight = forecaster.model_weights.get(model_id, 0)
                print(f"   {model_id}: {params}, AIC={aic:.2f}, Weight={weight:.3f}")
        else:
            print("❌ Model training failed")
            return False
        
        # Test forecasting
        print("\n🔮 Testing ensemble forecasting...")
        start_time = time.time()
        
        forecast_result = forecaster.forecast_ensemble(steps=10, confidence_level=0.95)
        
        forecast_time = time.time() - start_time
        print(f"✅ Generated 10-step forecast in {forecast_time:.3f}s")
        print(f"   Forecast range: {forecast_result.forecast.min():.2f} - {forecast_result.forecast.max():.2f}")
        print(f"   Models used: {len(forecast_result.model_params)}")
        
        # Test model summary
        print("\n📊 Testing model summary...")
        summary = forecaster.get_model_summary()
        
        print(f"✅ Summary generated:")
        print(f"   Total models: {summary['ensemble_info']['total_models']}")
        print(f"   Data points: {summary['data_info']['total_points']}")
        
        # Test model saving
        print("\n💾 Testing model persistence...")
        save_success = forecaster.save_models('test_enhanced_models.pkl')
        
        if save_success:
            print("✅ Models saved successfully")
            
            # Test loading
            new_forecaster = EnhancedARIMAForecaster()
            load_success = new_forecaster.load_models('test_enhanced_models.pkl')
            
            if load_success:
                print("✅ Models loaded successfully")
                print(f"   Loaded {len(new_forecaster.fitted_models)} models")
            else:
                print("❌ Model loading failed")
        else:
            print("❌ Model saving failed")
        
        # Performance summary
        print("\n⚡ Performance Summary:")
        print(f"   Parameter optimization: {param_time:.2f}s")
        print(f"   Model training: {train_time:.2f}s")
        print(f"   Forecasting: {forecast_time:.3f}s")
        print(f"   Total time: {param_time + train_time + forecast_time:.2f}s")
        
        print("\n🎉 All tests passed successfully!")
        print("\n📋 Enhanced Features Verified:")
        print("   ✅ Advanced parameter optimization")
        print("   ✅ Ensemble model training")
        print("   ✅ Weighted ensemble forecasting")
        print("   ✅ Model persistence and loading")
        print("   ✅ Comprehensive error handling")
        print("   ✅ Performance monitoring")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure enhanced_arima_forecasting.py is available")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """Compare with basic ARIMA if available"""
    print("\n🔬 Performance Comparison Test")
    print("=" * 40)
    
    try:
        # Test basic ARIMA from statsmodels
        from statsmodels.tsa.arima.model import ARIMA
        
        test_data = generate_simple_test_data(80)
        
        print("📊 Testing basic ARIMA...")
        start_time = time.time()
        
        # Simple parameter search
        best_aic = float('inf')
        best_params = (1, 1, 1)
        
        for p in range(3):
            for d in range(2):
                for q in range(3):
                    try:
                        model = ARIMA(test_data, order=(p, d, q))
                        fitted = model.fit()
                        if fitted.aic < best_aic:
                            best_aic = fitted.aic
                            best_params = (p, d, q)
                    except:
                        continue
        
        # Train best model
        basic_model = ARIMA(test_data, order=best_params)
        basic_fitted = basic_model.fit()
        
        # Generate forecast
        basic_forecast = basic_fitted.forecast(steps=10)
        
        basic_time = time.time() - start_time
        
        print(f"✅ Basic ARIMA completed in {basic_time:.2f}s")
        print(f"   Best parameters: {best_params}")
        print(f"   AIC: {best_aic:.2f}")
        
        # Now test enhanced version
        print("\n🚀 Testing Enhanced ARIMA...")
        start_time = time.time()
        
        from enhanced_arima_forecasting import EnhancedARIMAForecaster
        
        forecaster = EnhancedARIMAForecaster(enable_ensemble=False, enable_seasonal=False)
        forecaster.time_series = test_data
        forecaster.df = test_data.to_frame('revenue')
        forecaster.last_date = test_data.index.max()
        
        # Find parameters and train
        params = forecaster.find_optimal_parameters_advanced(max_p=2, max_q=2, use_bayesian=False)
        forecaster.train_ensemble_models(params[:1])  # Single model for fair comparison
        
        # Generate forecast
        enhanced_forecast = forecaster.forecast_ensemble(steps=10)
        
        enhanced_time = time.time() - start_time
        
        print(f"✅ Enhanced ARIMA completed in {enhanced_time:.2f}s")
        
        # Compare performance
        if enhanced_time < basic_time:
            improvement = basic_time / enhanced_time
            print(f"\n⚡ Enhanced ARIMA is {improvement:.1f}x faster!")
        else:
            print(f"\n📊 Basic ARIMA was faster by {enhanced_time / basic_time:.1f}x")
            print("   (Enhanced has more features and validation)")
        
        print(f"\n📈 Forecast comparison:")
        print(f"   Basic forecast range: {basic_forecast.min():.2f} - {basic_forecast.max():.2f}")
        print(f"   Enhanced forecast range: {enhanced_forecast.forecast.min():.2f} - {enhanced_forecast.forecast.max():.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Enhanced ARIMA Testing Suite")
    print("=" * 50)
    
    # Test 1: Basic functionality
    test1_success = test_enhanced_arima()
    
    # Test 2: Performance comparison
    test2_success = test_performance_comparison()
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Test Results Summary")
    print("=" * 50)
    
    if test1_success:
        print("✅ Enhanced ARIMA functionality: PASSED")
    else:
        print("❌ Enhanced ARIMA functionality: FAILED")
    
    if test2_success:
        print("✅ Performance comparison: PASSED")
    else:
        print("❌ Performance comparison: FAILED")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Enhanced ARIMA is working correctly.")
    elif test1_success:
        print("\n✅ Enhanced ARIMA is working, comparison test had issues.")
    else:
        print("\n❌ Tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
