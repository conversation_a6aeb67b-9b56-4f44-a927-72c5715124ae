#!/usr/bin/env python3
"""
Simplified ARIMA Forecasting API - Guaranteed to work
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
import json
import os
import re
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Check for required packages
try:
    from statsmodels.tsa.arima.model import ARIMA
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    ARIMA_AVAILABLE = True
    print("✅ ARIMA packages available")
except ImportError as e:
    print(f"⚠️ ARIMA packages not available: {e}")
    ARIMA_AVAILABLE = False

app = Flask(__name__)
CORS(app)

# Global variables
forecaster_data = {
    'time_series': None,
    'model': None,
    'fitted_model': None,
    'model_params': None,
    'last_date': None,
    'current_config': {
        'metric': 'revenue',
        'period': 'daily',
        'data_source': 'full_dataset'
    }
}

def parse_time_horizon(user_input):
    """Parse user input for time horizons"""
    user_input = user_input.lower().strip()

    # Direct mappings
    time_mappings = {
        'tomorrow': 1,
        'next week': 7,
        'next month': 30,
        'next year': 365,
        'next decade': 3650,
        'next century': 36500
    }

    if user_input in time_mappings:
        return time_mappings[user_input], user_input.title()

    # Pattern matching
    patterns = [
        (r'(\d+)\s+days?\s+from\s+now', lambda m: int(m.group(1))),
        (r'(\d+)\s+weeks?\s+later', lambda m: int(m.group(1)) * 7),
        (r'(\d+)\s+weeks?\s+from\s+now', lambda m: int(m.group(1)) * 7),
        (r'in\s+the\s+next\s+(\d+)\s+months?', lambda m: int(m.group(1)) * 30),
        (r'(\d+)\s+to\s+(\d+)\s+years?\s+from\s+now', lambda m: int(m.group(2)) * 365),
        (r'(\d+)\s+months?\s+from\s+now', lambda m: int(m.group(1)) * 30),
        (r'(\d+)\s+years?\s+from\s+now', lambda m: int(m.group(1)) * 365),
        (r'next\s+(\d+)\s+days?', lambda m: int(m.group(1))),
        (r'next\s+(\d+)\s+weeks?', lambda m: int(m.group(1)) * 7),
        (r'next\s+(\d+)\s+months?', lambda m: int(m.group(1)) * 30),
        (r'next\s+(\d+)\s+years?', lambda m: int(m.group(1)) * 365),
    ]

    for pattern, calculator in patterns:
        match = re.search(pattern, user_input)
        if match:
            steps = calculator(match)
            return steps, f"{steps} days ({user_input})"

    # Try to extract numbers
    numbers = re.findall(r'\d+', user_input)
    if numbers:
        return int(numbers[0]), f"{numbers[0]} days (interpreted from: {user_input})"

    # Default fallback
    return 30, f"30 days (default for: {user_input})"

def get_available_sample_parts():
    """Discover available sample part files"""
    available_parts = []
    sample_dir = '../sample'

    if os.path.exists(sample_dir):
        for i in range(1, 16):  # Check for part1.json through part15.json
            part_file = f'{sample_dir}/part{i}.json'
            if os.path.exists(part_file):
                try:
                    with open(part_file, 'r') as f:
                        data = json.load(f)

                    part_info = {
                        'part_number': i,
                        'file_path': part_file,
                        'transaction_count': len(data.get('transactions', [])),
                        'metadata': data.get('metadata', {})
                    }
                    available_parts.append(part_info)
                except Exception as e:
                    print(f"⚠️ Error reading {part_file}: {e}")

    return available_parts

def load_data(metric='revenue', period='daily', data_source='full_dataset', sample_part=None):
    """Load and prepare data"""
    try:
        print(f"Loading data: {metric}, {period}, {data_source}, part={sample_part}")

        # Determine data file
        if data_source == 'sample_data':
            # Support individual sample part selection
            if sample_part is not None:
                data_file = f'../sample/part{sample_part}.json'
            else:
                data_file = '../sample/part1.json'  # Default fallback

            if os.path.exists(data_file):
                with open(data_file, 'r') as f:
                    data = json.load(f)
                transactions = data['transactions']
                df = pd.DataFrame(transactions)
                print(f"Loaded sample part {sample_part or 1}: {len(df)} transactions")

                # Print metadata if available
                if 'metadata' in data:
                    metadata = data['metadata']
                    time_period = metadata.get('time_period', {})
                    print(f"Time period: {time_period.get('month', 'Unknown')}")
            else:
                print(f"Sample data not found: {data_file}")
                return False
        else:
            data_file = '../cleaned_transaction_data.csv'
            if os.path.exists(data_file):
                df = pd.read_csv(data_file)
                print(f"Loaded full dataset: {len(df)} transactions")
            else:
                print("Full dataset not found")
                return False

        # Convert datetime
        df['transactiontime'] = pd.to_datetime(df['transactiontime'])

        # Aggregate data
        if period == 'daily':
            df['date'] = df['transactiontime'].dt.date
            group_col = 'date'
        elif period == 'weekly':
            df['week'] = df['transactiontime'].dt.to_period('W')
            group_col = 'week'
        elif period == 'monthly':
            df['month'] = df['transactiontime'].dt.to_period('M')
            group_col = 'month'

        # Aggregate based on metric
        if metric == 'revenue':
            agg_data = df.groupby(group_col)['total_transaction_value'].sum()
        elif metric == 'transaction_count':
            agg_data = df.groupby(group_col).size()
        elif metric == 'quantity':
            agg_data = df.groupby(group_col)['numberofitemspurchased'].sum()

        # Convert to datetime index
        if period == 'daily':
            agg_data.index = pd.to_datetime(agg_data.index)
        else:
            agg_data.index = agg_data.index.to_timestamp()

        # Store in global variables
        forecaster_data['time_series'] = agg_data.sort_index()
        forecaster_data['last_date'] = forecaster_data['time_series'].index[-1]
        forecaster_data['current_config'] = {
            'metric': metric,
            'period': period,
            'data_source': data_source
        }

        print(f"Data prepared: {len(forecaster_data['time_series'])} data points")
        return True

    except Exception as e:
        print(f"Error loading data: {e}")
        return False

def train_model():
    """Train ARIMA model"""
    if forecaster_data['time_series'] is None:
        return False

    try:
        print("Training ARIMA model...")

        # Simple parameter search
        best_aic = float('inf')
        best_params = (1, 1, 1)

        for p in range(3):
            for d in range(2):
                for q in range(3):
                    try:
                        model = ARIMA(forecaster_data['time_series'].dropna(), order=(p, d, q))
                        fitted = model.fit()
                        if fitted.aic < best_aic:
                            best_aic = fitted.aic
                            best_params = (p, d, q)
                    except:
                        continue

        # Train final model
        forecaster_data['model_params'] = best_params
        model = ARIMA(forecaster_data['time_series'].dropna(), order=best_params)
        forecaster_data['fitted_model'] = model.fit()

        print(f"Model trained: ARIMA{best_params}, AIC: {best_aic:.2f}")
        return True

    except Exception as e:
        print(f"Training error: {e}")
        return False

# API Routes
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'arima_available': ARIMA_AVAILABLE,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/available-sample-parts', methods=['GET'])
def get_available_sample_parts_endpoint():
    """Get list of available sample parts for testing"""
    try:
        available_parts = get_available_sample_parts()

        return jsonify({
            'success': True,
            'available_parts': available_parts,
            'total_parts': len(available_parts),
            'message': f'Found {len(available_parts)} sample parts available for testing'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'available_parts': [],
            'total_parts': 0
        }), 500

@app.route('/prediction-options', methods=['GET'])
def get_prediction_options():
    """Get available prediction options"""
    return jsonify({
        'metrics': {
            'revenue': {
                'name': 'Revenue',
                'description': 'Total transaction value (financial performance)',
                'icon': '💰'
            },
            'transaction_count': {
                'name': 'Transaction Count',
                'description': 'Number of transactions (customer activity)',
                'icon': '📊'
            },
            'quantity': {
                'name': 'Quantity',
                'description': 'Total items purchased (demand forecasting)',
                'icon': '📦'
            }
        },
        'periods': {
            'daily': {'name': 'Daily', 'description': 'High granularity'},
            'weekly': {'name': 'Weekly', 'description': 'Medium granularity'},
            'monthly': {'name': 'Monthly', 'description': 'Low granularity'}
        },
        'data_sources': {
            'full_dataset': {'name': 'Full Dataset', 'description': 'Complete data'},
            'sample_data': {'name': 'Sample Data', 'description': 'Sample data'}
        }
    })

@app.route('/configure-prediction', methods=['POST'])
def configure_prediction():
    """Configure prediction settings"""
    try:
        data = request.get_json()
        metric = data.get('metric', 'revenue')
        period = data.get('period', 'daily')
        data_source = data.get('data_source', 'full_dataset')
        sample_part = data.get('sample_part', None)

        print(f"Configuring prediction: {metric}, {period}, {data_source}, part={sample_part}")

        # Load data
        if load_data(metric, period, data_source, sample_part):
            # Train model
            if train_model():
                return jsonify({
                    'success': True,
                    'message': 'Configuration updated and model trained successfully',
                    'config': forecaster_data['current_config'],
                    'data_points': len(forecaster_data['time_series']),
                    'model_params': forecaster_data['model_params']
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to train model'}), 500
        else:
            return jsonify({'success': False, 'error': 'Failed to load data'}), 500

    except Exception as e:
        print(f"Configuration error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/generate-forecast', methods=['POST'])
def generate_forecast():
    """Generate forecast"""
    try:
        data = request.get_json()
        user_input = data.get('horizon', 'next month')

        if forecaster_data['fitted_model'] is None:
            return jsonify({'success': False, 'error': 'No model trained'}), 400

        print(f"Generating forecast for: {user_input}")

        # Parse time horizon
        steps, description = parse_time_horizon(user_input)

        # Generate forecast
        forecast_result = forecaster_data['fitted_model'].get_forecast(steps=steps)
        forecast_values = forecast_result.predicted_mean
        confidence_intervals = forecast_result.conf_int()

        # Create future dates
        future_dates = pd.date_range(
            start=forecaster_data['last_date'] + pd.Timedelta(days=1),
            periods=steps,
            freq='D'
        )

        # Calculate insights
        recent_avg = forecaster_data['time_series'].tail(30).mean()
        forecast_avg = forecast_values.mean()
        growth_rate = ((forecast_avg - recent_avg) / recent_avg) * 100

        # Generate simple insights
        insights = []
        if growth_rate > 5:
            insights.append({
                'type': 'positive',
                'icon': '📈',
                'title': 'Growth Expected',
                'message': f'{growth_rate:.1f}% growth forecasted'
            })
        elif growth_rate < -5:
            insights.append({
                'type': 'warning',
                'icon': '📉',
                'title': 'Decline Expected',
                'message': f'{abs(growth_rate):.1f}% decline forecasted'
            })
        else:
            insights.append({
                'type': 'neutral',
                'icon': '📊',
                'title': 'Stable Trend',
                'message': 'Steady performance expected'
            })

        return jsonify({
            'success': True,
            'forecast': {
                'user_input': user_input,
                'description': description,
                'steps': steps,
                'forecast_dates': future_dates.strftime('%Y-%m-%d').tolist(),
                'forecast_values': forecast_values.tolist(),
                'lower_ci': confidence_intervals.iloc[:, 0].tolist(),
                'upper_ci': confidence_intervals.iloc[:, 1].tolist(),
                'total_forecast': float(forecast_values.sum()),
                'average_forecast': float(forecast_avg),
                'growth_rate': float(growth_rate),
                'insights': insights,
                'config': forecaster_data['current_config']
            }
        })

    except Exception as e:
        print(f"Forecast error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/model-status', methods=['GET'])
def get_model_status():
    """Get model status"""
    try:
        if forecaster_data['fitted_model'] is None:
            return jsonify({'model_loaded': False})

        # Extract AIC and BIC values
        aic_value = None
        bic_value = None

        if forecaster_data['fitted_model']:
            try:
                aic_value = float(forecaster_data['fitted_model'].aic)
                bic_value = float(forecaster_data['fitted_model'].bic)
                print(f"📊 Model metrics - AIC: {aic_value}, BIC: {bic_value}")
            except Exception as e:
                print(f"⚠️ Error extracting AIC/BIC: {e}")

        return jsonify({
            'model_loaded': True,
            'config': forecaster_data['current_config'],
            'model_params': forecaster_data['model_params'],
            'data_points': len(forecaster_data['time_series']) if forecaster_data['time_series'] is not None else 0,
            'last_data_point': forecaster_data['last_date'].isoformat() if forecaster_data['last_date'] else None,
            'aic': aic_value,
            'bic': bic_value
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/database-stats', methods=['GET'])
def get_database_stats():
    """Get database statistics"""
    try:
        stats = {'total_transactions': 0, 'total_revenue': 0, 'unique_users': 0, 'unique_countries': 0}

        if os.path.exists('../cleaned_transaction_data.csv'):
            df = pd.read_csv('../cleaned_transaction_data.csv')
            stats = {
                'total_transactions': len(df),
                'total_revenue': float(df['total_transaction_value'].sum()),
                'unique_users': int(df['userid'].nunique()) if 'userid' in df.columns else 0,
                'unique_countries': int(df['country'].nunique()) if 'country' in df.columns else 0
            }

        return jsonify(stats)

    except Exception as e:
        return jsonify({'total_transactions': 0, 'total_revenue': 0, 'unique_users': 0, 'unique_countries': 0})

if __name__ == '__main__':
    print("🚀 Starting Simplified ARIMA Forecasting API...")
    print("📊 Features:")
    print("   • Multiple prediction options (revenue, transactions, quantity)")
    print("   • Flexible time horizons (natural language support)")
    print("   • Business insights and recommendations")
    print()
    print("🌐 API will be available at: http://localhost:8001")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)

    app.run(host='0.0.0.0', port=8001, debug=True)
