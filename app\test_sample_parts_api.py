#!/usr/bin/env python3
"""
Test script to verify sample parts API functionality
"""

import requests
import json
import os
import sys

def test_sample_parts_discovery():
    """Test the sample parts discovery functionality"""
    print("🧪 Testing Sample Parts API")
    print("=" * 50)
    
    # Test 1: Check if sample files exist
    print("1. Checking sample files...")
    sample_dir = '../sample'
    
    if not os.path.exists(sample_dir):
        print(f"❌ Sample directory not found: {sample_dir}")
        return False
    
    sample_files = []
    for i in range(1, 11):
        file_path = f'{sample_dir}/part{i}.json'
        if os.path.exists(file_path):
            sample_files.append(file_path)
            print(f"   ✅ Found: {file_path}")
        else:
            print(f"   ❌ Missing: {file_path}")
    
    print(f"   📊 Total sample files found: {len(sample_files)}")
    
    if len(sample_files) == 0:
        print("❌ No sample files found!")
        return False
    
    # Test 2: Test direct function call
    print("\n2. Testing direct function call...")
    try:
        sys.path.append('.')
        from arima_api import SmartARIMAForecaster
        
        forecaster = SmartARIMAForecaster()
        available_parts = forecaster.get_available_sample_parts()
        
        print(f"   📊 Available parts from function: {len(available_parts)}")
        for part in available_parts:
            metadata = part.get('metadata', {})
            time_period = metadata.get('time_period', {})
            month = time_period.get('month', 'Unknown')
            print(f"   ✅ Part {part['part_number']}: {month} ({part['transaction_count']} transactions)")
        
    except Exception as e:
        print(f"   ❌ Error testing function: {e}")
        return False
    
    # Test 3: Test API endpoint (if server is running)
    print("\n3. Testing API endpoint...")
    try:
        response = requests.get('http://localhost:8001/available-sample-parts', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                parts = data.get('available_parts', [])
                print(f"   ✅ API returned {len(parts)} parts")
                for part in parts[:3]:  # Show first 3
                    metadata = part.get('metadata', {})
                    time_period = metadata.get('time_period', {})
                    month = time_period.get('month', 'Unknown')
                    print(f"   📊 Part {part['part_number']}: {month}")
            else:
                print(f"   ❌ API returned error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ API returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️ API server not running (this is OK for file testing)")
    except Exception as e:
        print(f"   ❌ Error testing API: {e}")
    
    print("\n✅ Sample parts discovery test completed successfully!")
    return True

def test_sample_part_loading():
    """Test loading individual sample parts"""
    print("\n🔍 Testing Individual Sample Part Loading")
    print("=" * 50)
    
    try:
        sys.path.append('.')
        from arima_api import SmartARIMAForecaster
        
        forecaster = SmartARIMAForecaster()
        
        # Test loading different parts
        test_parts = [1, 3, 5]
        
        for part_num in test_parts:
            print(f"\n📊 Testing Part {part_num}...")
            
            success = forecaster.load_data(
                metric='revenue',
                period='daily',
                data_source='sample_data',
                sample_part=part_num
            )
            
            if success:
                print(f"   ✅ Successfully loaded part {part_num}")
                print(f"   📈 Time series length: {len(forecaster.time_series) if forecaster.time_series is not None else 0}")
                
                if hasattr(forecaster, 'sample_metadata') and forecaster.sample_metadata:
                    metadata = forecaster.sample_metadata
                    time_period = metadata.get('time_period', {})
                    print(f"   📅 Time period: {time_period.get('month', 'Unknown')}")
                
            else:
                print(f"   ❌ Failed to load part {part_num}")
                return False
        
        print("\n✅ Individual sample part loading test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing sample part loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 SAMPLE PARTS API TEST SUITE")
    print("=" * 60)
    
    success = True
    
    # Run tests
    if not test_sample_parts_discovery():
        success = False
    
    if not test_sample_part_loading():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("\n💡 Next steps:")
        print("1. Start the API server: python arima_api.py")
        print("2. Open the web interface: http://localhost:3000")
        print("3. Select 'Sample Data' and choose a specific part")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure sample/ directory exists with part1.json through part10.json")
        print("2. Check file permissions")
        print("3. Verify JSON file format")
    
    return success

if __name__ == "__main__":
    main()
