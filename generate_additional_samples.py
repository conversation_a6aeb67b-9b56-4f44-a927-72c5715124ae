#!/usr/bin/env python3
"""
Generate 5 additional sample parts (part11.json through part15.json)
with realistic transaction data for different time periods
"""

import json
import random
from datetime import datetime, timedelta
import uuid

def generate_realistic_transaction(base_date, user_id_pool, item_pool):
    """Generate a realistic transaction"""
    
    # Random time within the day
    hour = random.choices(
        range(24), 
        weights=[0.1, 0.1, 0.1, 0.1, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.5, 
                1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.3, 0.2]
    )[0]
    minute = random.randint(0, 59)
    
    transaction_time = base_date.replace(hour=hour, minute=minute)
    
    # Select random item
    item = random.choice(item_pool)
    
    # Generate quantities with realistic distribution
    quantity_weights = [0.4, 0.3, 0.15, 0.08, 0.04, 0.02, 0.01]  # Favor smaller quantities
    quantity_ranges = [
        range(1, 6),      # 1-5 items (40%)
        range(6, 21),     # 6-20 items (30%)
        range(21, 51),    # 21-50 items (15%)
        range(51, 101),   # 51-100 items (8%)
        range(101, 201),  # 101-200 items (4%)
        range(201, 501),  # 201-500 items (2%)
        range(501, 1001)  # 501-1000 items (1%)
    ]
    
    selected_range = random.choices(quantity_ranges, weights=quantity_weights)[0]
    quantity = random.choice(selected_range)
    
    # Price per item (realistic UK retail prices)
    cost_per_item = round(random.uniform(0.50, 25.00), 2)
    
    # Sometimes have null user IDs (guest purchases)
    user_id = random.choice(user_id_pool) if random.random() > 0.05 else None
    
    # Generate transaction ID
    transaction_id = random.randint(6000000, 7000000)
    
    # Calculate total
    total_value = round(quantity * cost_per_item, 2)
    
    transaction = {
        "userid": user_id,
        "transactionid": transaction_id,
        "transactiontime": transaction_time.strftime("%Y-%m-%dT%H:%M:%S"),
        "itemcode": item["code"],
        "itemdescription": item["description"],
        "numberofitemspurchased": quantity,
        "costperitem": cost_per_item,
        "country": random.choices(
            ["United Kingdom", "France", "Germany", "Spain", "Netherlands", "Belgium", "Italy"],
            weights=[0.7, 0.08, 0.07, 0.05, 0.04, 0.03, 0.03]
        )[0],
        "total_transaction_value": total_value,
        "transaction_year": transaction_time.year,
        "transaction_month": transaction_time.month,
        "transaction_day": transaction_time.day,
        "transaction_weekday": transaction_time.weekday(),
        "transaction_hour": transaction_time.hour
    }
    
    return transaction

def generate_sample_part(part_number, time_period_info, sample_size=100):
    """Generate a complete sample part file"""
    
    # User ID pool (realistic customer IDs)
    user_id_pool = [random.randint(300000, 400000) for _ in range(50)]
    
    # Item pool with realistic product descriptions
    item_pool = [
        {"code": 1782459, "description": "ASSORTED COLOUR BIRD ORNAMENT"},
        {"code": 1786029, "description": "SCANDINAVIAN REDS RIBBONS"},
        {"code": 1784567, "description": "VINTAGE CHRISTMAS DECORATIONS"},
        {"code": 1785123, "description": "CERAMIC COFFEE MUG SET"},
        {"code": 1783456, "description": "HANDMADE WOODEN TOYS"},
        {"code": 1787890, "description": "FLORAL PATTERN TABLECLOTH"},
        {"code": 1782345, "description": "ARTISAN SOAP COLLECTION"},
        {"code": 1786789, "description": "VINTAGE STYLE PICTURE FRAMES"},
        {"code": 1784321, "description": "GARDEN HERB STARTER KIT"},
        {"code": 1785678, "description": "LUXURY SCENTED CANDLES"},
        {"code": 1783789, "description": "CHILDREN'S EDUCATIONAL BOOKS"},
        {"code": 1787123, "description": "KITCHEN UTENSIL SET"},
        {"code": 1782678, "description": "DECORATIVE THROW PILLOWS"},
        {"code": 1786456, "description": "ORGANIC TEA SELECTION"},
        {"code": 1784789, "description": "HANDCRAFTED JEWELRY BOX"},
        {"code": 1785234, "description": "SEASONAL FLOWER SEEDS"},
        {"code": 1783567, "description": "PREMIUM CHOCOLATE ASSORTMENT"},
        {"code": 1787456, "description": "VINTAGE STYLE WALL CLOCK"},
        {"code": 1782890, "description": "BAMBOO KITCHEN ACCESSORIES"},
        {"code": 1786123, "description": "ARTISAN POTTERY COLLECTION"}
    ]
    
    # Generate date range
    start_date = datetime.strptime(time_period_info["start_date"], "%Y-%m-%d")
    end_date = datetime.strptime(time_period_info["end_date"], "%Y-%m-%d")
    
    # Generate random dates within the period
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)
    
    # Generate transactions
    transactions = []
    for _ in range(sample_size):
        random_date = random.choice(date_range)
        transaction = generate_realistic_transaction(random_date, user_id_pool, item_pool)
        transactions.append(transaction)
    
    # Sort transactions by time
    transactions.sort(key=lambda x: x["transactiontime"])
    
    # Create metadata
    metadata = {
        "part_number": part_number,
        "time_period": time_period_info,
        "sample_size": sample_size,
        "generated_at": datetime.now().isoformat()
    }
    
    # Create complete sample part
    sample_part = {
        "metadata": metadata,
        "transactions": transactions
    }
    
    return sample_part

def main():
    """Generate 5 additional sample parts"""
    print("🔄 Generating 5 Additional Sample Parts")
    print("=" * 50)
    
    # Define time periods for the new sample parts
    time_periods = [
        {
            "part_number": 11,
            "time_period": {
                "month": "2018-11",
                "start_date": "2018-11-01",
                "end_date": "2018-11-30"
            },
            "description": "Black Friday / Pre-Christmas shopping season"
        },
        {
            "part_number": 12,
            "time_period": {
                "month": "2018-08",
                "start_date": "2018-08-01",
                "end_date": "2018-08-31"
            },
            "description": "Late summer / Back-to-school period"
        },
        {
            "part_number": 13,
            "time_period": {
                "month": "2019-03",
                "start_date": "2019-03-01",
                "end_date": "2019-03-31"
            },
            "description": "Spring season / Easter preparations"
        },
        {
            "part_number": 14,
            "time_period": {
                "month": "2018-02",
                "start_date": "2018-02-01",
                "end_date": "2018-02-28"
            },
            "description": "Valentine's Day / Winter clearance"
        },
        {
            "part_number": 15,
            "time_period": {
                "month": "2018-01",
                "start_date": "2018-01-01",
                "end_date": "2018-01-31"
            },
            "description": "New Year / Post-holiday period"
        }
    ]
    
    # Generate each sample part
    for period_info in time_periods:
        part_number = period_info["part_number"]
        time_period = period_info["time_period"]
        description = period_info["description"]
        
        print(f"📊 Generating Part {part_number}: {time_period['month']} ({description})")
        
        # Generate sample part data
        sample_part = generate_sample_part(part_number, time_period)
        
        # Save to file
        filename = f"sample/part{part_number}.json"
        with open(filename, 'w') as f:
            json.dump(sample_part, f, indent=2)
        
        print(f"✅ Created {filename} with {len(sample_part['transactions'])} transactions")
        
        # Show sample statistics
        transactions = sample_part['transactions']
        total_revenue = sum(t['total_transaction_value'] for t in transactions)
        avg_transaction = total_revenue / len(transactions)
        
        print(f"   💰 Total Revenue: £{total_revenue:,.2f}")
        print(f"   📊 Avg Transaction: £{avg_transaction:.2f}")
        print(f"   📅 Date Range: {time_period['start_date']} to {time_period['end_date']}")
        print()
    
    print("🎉 Successfully generated 5 additional sample parts!")
    print("\n📋 Summary of all sample parts:")
    print("Part 1-10: Original sample parts")
    print("Part 11: November 2018 (Black Friday season)")
    print("Part 12: August 2018 (Late summer)")
    print("Part 13: March 2019 (Spring/Easter)")
    print("Part 14: February 2018 (Valentine's Day)")
    print("Part 15: January 2018 (New Year)")
    print("\n💡 These parts provide diverse seasonal patterns for comprehensive ARIMA testing!")

if __name__ == "__main__":
    main()
