#!/usr/bin/env python3
"""
Enhanced ARIMA Forecasting API for Web Dashboard
Supports multiple prediction options and interactive forecasting
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import ARIMA components
try:
    from statsmodels.tsa.arima.model import ARIMA
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    ARIMA_AVAILABLE = True
except ImportError:
    print("⚠️ ARIMA packages not available. Install with: pip install statsmodels scikit-learn")
    ARIMA_AVAILABLE = False

app = Flask(__name__)
CORS(app)

class SmartARIMAForecaster:
    """Enhanced ARIMA forecaster with multiple prediction options"""

    def __init__(self):
        self.time_series = None
        self.model = None
        self.fitted_model = None
        self.model_params = None
        self.last_date = None
        self.current_config = {
            'metric': 'revenue',
            'period': 'daily',
            'data_source': 'full_dataset',
            'sample_part': None
        }
        self.sample_metadata = None

        # Time horizon mappings
        self.time_mappings = {
            'tomorrow': 1,
            'next week': 7,
            'next month': 30,
            'next year': 365,
            'next decade': 3650,
            'next century': 36500
        }

    def get_available_sample_parts(self):
        """Discover available sample part files"""
        available_parts = []
        sample_dir = '../sample'

        if os.path.exists(sample_dir):
            for i in range(1, 16):  # Check for part1.json through part15.json
                part_file = f'{sample_dir}/part{i}.json'
                if os.path.exists(part_file):
                    try:
                        with open(part_file, 'r') as f:
                            data = json.load(f)

                        part_info = {
                            'part_number': i,
                            'file_path': part_file,
                            'transaction_count': len(data.get('transactions', [])),
                            'metadata': data.get('metadata', {})
                        }
                        available_parts.append(part_info)
                    except Exception as e:
                        print(f"⚠️ Error reading {part_file}: {e}")

        return available_parts

    def load_data(self, metric='revenue', period='daily', data_source='full_dataset', sample_part=None):
        """Load and prepare data based on configuration"""
        try:
            # Determine data file
            if data_source == 'sample_data':
                # Support individual sample part selection
                if sample_part is not None:
                    data_file = f'../sample/part{sample_part}.json'
                else:
                    data_file = '../sample/part1.json'  # Default fallback

                if os.path.exists(data_file):
                    with open(data_file, 'r') as f:
                        data = json.load(f)
                    transactions = data['transactions']
                    df = pd.DataFrame(transactions)
                    print(f"📊 Loaded sample part {sample_part or 1}: {len(df)} transactions")

                    # Store metadata for reference
                    if 'metadata' in data:
                        self.sample_metadata = data['metadata']
                        print(f"📅 Time period: {data['metadata'].get('time_period', {}).get('month', 'Unknown')}")
                else:
                    print(f"❌ Sample file not found: {data_file}")
                    return None
            else:
                data_file = '../cleaned_transaction_data.csv'
                if os.path.exists(data_file):
                    df = pd.read_csv(data_file)
                else:
                    return None

            # Convert datetime
            df['transactiontime'] = pd.to_datetime(df['transactiontime'])

            # Aggregate data based on period and metric
            if period == 'daily':
                df['date'] = df['transactiontime'].dt.date
                group_col = 'date'
                freq = 'D'
            elif period == 'weekly':
                df['week'] = df['transactiontime'].dt.to_period('W')
                group_col = 'week'
                freq = 'W'
            elif period == 'monthly':
                df['month'] = df['transactiontime'].dt.to_period('M')
                group_col = 'month'
                freq = 'M'

            # Aggregate based on metric
            if metric == 'revenue':
                agg_data = df.groupby(group_col)['total_transaction_value'].sum()
            elif metric == 'transaction_count':
                agg_data = df.groupby(group_col).size()
            elif metric == 'quantity':
                agg_data = df.groupby(group_col)['numberofitemspurchased'].sum()

            # Convert to datetime index
            if period == 'daily':
                agg_data.index = pd.to_datetime(agg_data.index)
            else:
                agg_data.index = agg_data.index.to_timestamp()

            # Fill missing dates
            full_range = pd.date_range(start=agg_data.index.min(),
                                     end=agg_data.index.max(), freq=freq)
            agg_data = agg_data.reindex(full_range, fill_value=0)

            self.time_series = agg_data
            self.last_date = self.time_series.index[-1]
            self.current_config = {
                'metric': metric,
                'period': period,
                'data_source': data_source
            }

            return True

        except Exception as e:
            print(f"Error loading data: {e}")
            return None

    def parse_time_horizon(self, user_input: str) -> Tuple[int, str]:
        """Parse user input for time horizons"""
        user_input = user_input.lower().strip()

        # Direct mappings
        if user_input in self.time_mappings:
            return self.time_mappings[user_input], user_input.title()

        # Pattern matching
        patterns = [
            (r'(\d+)\s+days?\s+from\s+now', lambda m: int(m.group(1))),
            (r'(\d+)\s+weeks?\s+later', lambda m: int(m.group(1)) * 7),
            (r'(\d+)\s+weeks?\s+from\s+now', lambda m: int(m.group(1)) * 7),
            (r'in\s+the\s+next\s+(\d+)\s+months?', lambda m: int(m.group(1)) * 30),
            (r'(\d+)\s+to\s+(\d+)\s+years?\s+from\s+now', lambda m: int(m.group(2)) * 365),
            (r'(\d+)\s+months?\s+from\s+now', lambda m: int(m.group(1)) * 30),
            (r'(\d+)\s+years?\s+from\s+now', lambda m: int(m.group(1)) * 365),
            (r'next\s+(\d+)\s+days?', lambda m: int(m.group(1))),
            (r'next\s+(\d+)\s+weeks?', lambda m: int(m.group(1)) * 7),
            (r'next\s+(\d+)\s+months?', lambda m: int(m.group(1)) * 30),
            (r'next\s+(\d+)\s+years?', lambda m: int(m.group(1)) * 365),
        ]

        for pattern, calculator in patterns:
            match = re.search(pattern, user_input)
            if match:
                steps = calculator(match)
                return steps, f"{steps} days ({user_input})"

        # Try to extract numbers
        numbers = re.findall(r'\d+', user_input)
        if numbers:
            return int(numbers[0]), f"{numbers[0]} days (interpreted from: {user_input})"

        # Default fallback
        return 30, f"30 days (default for: {user_input})"

    def train_model(self, order=None):
        """Train ARIMA model"""
        if self.time_series is None:
            return False

        try:
            if order is None:
                # Simple parameter search
                best_aic = float('inf')
                best_params = (1, 1, 1)

                for p in range(3):
                    for d in range(2):
                        for q in range(3):
                            try:
                                model = ARIMA(self.time_series.dropna(), order=(p, d, q))
                                fitted = model.fit()
                                if fitted.aic < best_aic:
                                    best_aic = fitted.aic
                                    best_params = (p, d, q)
                            except:
                                continue

                order = best_params

            self.model_params = order
            self.model = ARIMA(self.time_series.dropna(), order=order)
            self.fitted_model = self.model.fit()

            return True

        except Exception as e:
            print(f"Training error: {e}")
            return False

    def generate_forecast(self, user_input: str, confidence_level=0.95):
        """Generate forecast with business insights"""
        if self.fitted_model is None:
            return None

        try:
            # Parse time horizon
            steps, description = self.parse_time_horizon(user_input)

            # Generate forecast
            forecast_result = self.fitted_model.get_forecast(steps=steps)
            forecast_values = forecast_result.predicted_mean
            confidence_intervals = forecast_result.conf_int(alpha=1-confidence_level)

            # Create future dates
            freq = pd.infer_freq(self.time_series.index) or 'D'
            future_dates = pd.date_range(
                start=self.last_date + pd.Timedelta(days=1),
                periods=steps,
                freq=freq
            )

            # Calculate insights
            recent_avg = self.time_series.tail(30).mean()
            forecast_avg = forecast_values.mean()
            growth_rate = ((forecast_avg - recent_avg) / recent_avg) * 100
            volatility = forecast_values.std()

            # Generate business insights
            insights = self.generate_business_insights(growth_rate, volatility)

            return {
                'user_input': user_input,
                'description': description,
                'steps': steps,
                'forecast_dates': future_dates.strftime('%Y-%m-%d').tolist(),
                'forecast_values': forecast_values.tolist(),
                'lower_ci': confidence_intervals.iloc[:, 0].tolist(),
                'upper_ci': confidence_intervals.iloc[:, 1].tolist(),
                'total_forecast': float(forecast_values.sum()),
                'average_forecast': float(forecast_avg),
                'growth_rate': float(growth_rate),
                'volatility': float(volatility),
                'insights': insights,
                'config': self.current_config
            }

        except Exception as e:
            print(f"Forecast error: {e}")
            return None

    def generate_business_insights(self, growth_rate, volatility):
        """Generate metric-specific business insights"""
        metric = self.current_config['metric']
        insights = []

        if metric == 'revenue':
            if growth_rate > 5:
                insights.append({
                    'type': 'positive',
                    'icon': '📈',
                    'title': 'Strong Revenue Growth',
                    'message': f'Expected {growth_rate:.1f}% growth - consider scaling operations'
                })
            elif growth_rate < -5:
                insights.append({
                    'type': 'warning',
                    'icon': '📉',
                    'title': 'Revenue Decline',
                    'message': f'{abs(growth_rate):.1f}% decline expected - review strategies'
                })
            else:
                insights.append({
                    'type': 'neutral',
                    'icon': '📊',
                    'title': 'Stable Revenue',
                    'message': 'Steady performance - maintain current operations'
                })

        elif metric == 'transaction_count':
            if growth_rate > 10:
                insights.append({
                    'type': 'positive',
                    'icon': '🚀',
                    'title': 'High Activity Growth',
                    'message': 'Prepare for increased customer service needs'
                })
            elif growth_rate < -10:
                insights.append({
                    'type': 'warning',
                    'icon': '⚠️',
                    'title': 'Declining Activity',
                    'message': 'Focus on customer acquisition and retention'
                })

        elif metric == 'quantity':
            if growth_rate > 15:
                insights.append({
                    'type': 'positive',
                    'icon': '📈',
                    'title': 'High Demand Growth',
                    'message': 'Ensure adequate inventory levels'
                })
            elif growth_rate < -15:
                insights.append({
                    'type': 'warning',
                    'icon': '📉',
                    'title': 'Declining Demand',
                    'message': 'Review product mix and pricing'
                })

        # Risk assessment
        cv = (volatility / abs(self.time_series.mean())) * 100
        if cv < 10:
            insights.append({
                'type': 'positive',
                'icon': '✅',
                'title': 'Low Risk',
                'message': 'Highly predictable forecast'
            })
        elif cv < 25:
            insights.append({
                'type': 'neutral',
                'icon': '⚠️',
                'title': 'Moderate Risk',
                'message': 'Monitor performance closely'
            })
        else:
            insights.append({
                'type': 'warning',
                'icon': '🚨',
                'title': 'High Risk',
                'message': 'Consider additional risk management'
            })

        return insights

# Initialize forecaster
forecaster = SmartARIMAForecaster()

# API Routes
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'arima_available': ARIMA_AVAILABLE,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/prediction-options', methods=['GET'])
def get_prediction_options():
    """Get available prediction options"""
    return jsonify({
        'metrics': {
            'revenue': {
                'name': 'Revenue',
                'description': 'Total transaction value (financial performance)',
                'icon': '💰',
                'use_cases': ['Budget planning', 'Growth analysis', 'Financial forecasting']
            },
            'transaction_count': {
                'name': 'Transaction Count',
                'description': 'Number of transactions (customer activity)',
                'icon': '📊',
                'use_cases': ['Customer behavior', 'Traffic forecasting', 'Capacity planning']
            },
            'quantity': {
                'name': 'Quantity',
                'description': 'Total items purchased (demand forecasting)',
                'icon': '📦',
                'use_cases': ['Inventory management', 'Supply chain', 'Demand planning']
            }
        },
        'periods': {
            'daily': {
                'name': 'Daily',
                'description': 'High granularity for short to medium-term forecasting',
                'best_for': 'Days to months'
            },
            'weekly': {
                'name': 'Weekly',
                'description': 'Medium granularity for balanced predictions',
                'best_for': 'Weeks to quarters'
            },
            'monthly': {
                'name': 'Monthly',
                'description': 'Low granularity for long-term trends',
                'best_for': 'Months to years'
            }
        },
        'data_sources': {
            'full_dataset': {
                'name': 'Full Dataset',
                'description': 'Complete transaction data (~20K transactions)',
                'accuracy': 'High'
            },
            'sample_data': {
                'name': 'Sample Data',
                'description': 'Sample transaction data (~1K transactions)',
                'accuracy': 'Medium'
            }
        }
    })

@app.route('/configure-prediction', methods=['POST'])
def configure_prediction():
    """Configure prediction settings"""
    try:
        data = request.get_json()
        metric = data.get('metric', 'revenue')
        period = data.get('period', 'daily')
        data_source = data.get('data_source', 'full_dataset')
        sample_part = data.get('sample_part', None)

        # Load data with new configuration
        success = forecaster.load_data(metric, period, data_source, sample_part)

        if success:
            # Train model
            training_success = forecaster.train_model()

            if training_success:
                return jsonify({
                    'success': True,
                    'message': 'Configuration updated and model trained successfully',
                    'config': forecaster.current_config,
                    'data_points': len(forecaster.time_series),
                    'date_range': {
                        'start': forecaster.time_series.index[0].isoformat(),
                        'end': forecaster.time_series.index[-1].isoformat()
                    },
                    'model_params': forecaster.model_params,
                    'statistics': {
                        'mean': float(forecaster.time_series.mean()),
                        'std': float(forecaster.time_series.std()),
                        'min': float(forecaster.time_series.min()),
                        'max': float(forecaster.time_series.max())
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to train model'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to load data'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/generate-forecast', methods=['POST'])
def generate_forecast():
    """Generate smart forecast based on user input"""
    try:
        data = request.get_json()
        user_input = data.get('horizon', 'next month')
        confidence_level = data.get('confidence_level', 0.95)

        if forecaster.fitted_model is None:
            return jsonify({
                'success': False,
                'error': 'No model trained. Please configure prediction settings first.'
            }), 400

        # Generate forecast
        forecast_result = forecaster.generate_forecast(user_input, confidence_level)

        if forecast_result:
            return jsonify({
                'success': True,
                'forecast': forecast_result
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to generate forecast'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/model-status', methods=['GET'])
def get_model_status():
    """Get current model status and configuration"""
    try:
        if forecaster.fitted_model is None:
            return jsonify({
                'model_loaded': False,
                'message': 'No model trained'
            })

        # Ensure AIC and BIC are properly extracted
        aic_value = None
        bic_value = None

        if forecaster.fitted_model:
            try:
                aic_value = float(forecaster.fitted_model.aic)
                bic_value = float(forecaster.fitted_model.bic)
                print(f"📊 Model metrics - AIC: {aic_value}, BIC: {bic_value}")
            except Exception as e:
                print(f"⚠️ Error extracting AIC/BIC: {e}")

        return jsonify({
            'model_loaded': True,
            'config': forecaster.current_config,
            'model_params': forecaster.model_params,
            'data_points': len(forecaster.time_series) if forecaster.time_series is not None else 0,
            'last_data_point': forecaster.last_date.isoformat() if forecaster.last_date else None,
            'aic': aic_value,
            'bic': bic_value
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/available-sample-parts', methods=['GET'])
def get_available_sample_parts():
    """Get list of available sample parts for testing"""
    try:
        available_parts = forecaster.get_available_sample_parts()

        return jsonify({
            'success': True,
            'available_parts': available_parts,
            'total_parts': len(available_parts),
            'message': f'Found {len(available_parts)} sample parts available for testing'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'available_parts': [],
            'total_parts': 0
        }), 500

@app.route('/database-stats', methods=['GET'])
def get_database_stats():
    """Get database statistics"""
    try:
        # Try to load full dataset for stats
        stats = {}

        if os.path.exists('../cleaned_transaction_data.csv'):
            df = pd.read_csv('../cleaned_transaction_data.csv')
            stats = {
                'total_transactions': len(df),
                'total_revenue': float(df['total_transaction_value'].sum()),
                'unique_users': int(df['userid'].nunique()) if 'userid' in df.columns else 0,
                'unique_countries': int(df['country'].nunique()) if 'country' in df.columns else 0,
                'date_range': {
                    'start': pd.to_datetime(df['transactiontime']).min().isoformat(),
                    'end': pd.to_datetime(df['transactiontime']).max().isoformat()
                }
            }
        else:
            # Fallback to sample data
            sample_file = '../sample/part1.json'
            if os.path.exists(sample_file):
                with open(sample_file, 'r') as f:
                    data = json.load(f)
                transactions = data['transactions']
                df = pd.DataFrame(transactions)

                stats = {
                    'total_transactions': len(df),
                    'total_revenue': float(df['total_transaction_value'].sum()),
                    'unique_users': int(df['userid'].nunique()) if 'userid' in df.columns else 0,
                    'unique_countries': int(df['country'].nunique()) if 'country' in df.columns else 0,
                    'date_range': {
                        'start': pd.to_datetime(df['transactiontime']).min().isoformat(),
                        'end': pd.to_datetime(df['transactiontime']).max().isoformat()
                    }
                }

        return jsonify(stats)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'total_transactions': 0,
            'total_revenue': 0,
            'unique_users': 0,
            'unique_countries': 0
        })

@app.route('/historical-data', methods=['GET'])
def get_historical_data():
    """Get historical data for charts"""
    try:
        if forecaster.time_series is None:
            return jsonify({
                'success': False,
                'error': 'No data loaded'
            }), 400

        # Get last 100 data points for chart
        recent_data = forecaster.time_series.tail(100)

        return jsonify({
            'success': True,
            'dates': recent_data.index.strftime('%Y-%m-%d').tolist(),
            'values': recent_data.values.tolist(),
            'config': forecaster.current_config
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Enhanced ARIMA Forecasting API...")
    print("📊 Supports multiple prediction options:")
    print("   • Revenue, Transaction Count, Quantity forecasting")
    print("   • Daily, Weekly, Monthly periods")
    print("   • Natural language time horizons")
    print("   • Business insights and recommendations")
    print()
    print("🌐 API will be available at: http://localhost:8001")
    print("📋 Available endpoints:")
    print("   • GET  /health - Health check")
    print("   • GET  /prediction-options - Available options")
    print("   • POST /configure-prediction - Configure settings")
    print("   • POST /generate-forecast - Generate forecast")
    print("   • GET  /model-status - Model status")
    print("   • GET  /database-stats - Database statistics")
    print("   • GET  /historical-data - Historical data")
    print()
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)

    app.run(host='0.0.0.0', port=8001, debug=True)
