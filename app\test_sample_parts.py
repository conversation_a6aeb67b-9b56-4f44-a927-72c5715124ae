#!/usr/bin/env python3
"""
Test script for individual sample part ARIMA model testing
Demonstrates how to use each sample part separately for testing
"""

import sys
import os
sys.path.append('..')

from arima_api import SmartARIMAForecaster
import json
import pandas as pd

def test_individual_sample_parts():
    """Test ARIMA model with each sample part individually"""
    print("🧪 Testing ARIMA Model with Individual Sample Parts")
    print("=" * 60)
    
    # Initialize forecaster
    forecaster = SmartARIMAForecaster()
    
    # Get available sample parts
    available_parts = forecaster.get_available_sample_parts()
    
    if not available_parts:
        print("❌ No sample parts found!")
        print("Make sure sample/ directory exists with part1.json through part10.json")
        return
    
    print(f"📊 Found {len(available_parts)} sample parts available for testing")
    print()
    
    # Test each sample part individually
    results = []
    
    for part_info in available_parts:
        part_number = part_info['part_number']
        metadata = part_info['metadata']
        transaction_count = part_info['transaction_count']
        
        print(f"🔍 Testing Sample Part {part_number}")
        print(f"   📅 Time Period: {metadata.get('time_period', {}).get('month', 'Unknown')}")
        print(f"   📊 Transactions: {transaction_count}")
        
        try:
            # Load data for this specific part
            success = forecaster.load_data(
                metric='revenue',
                period='daily', 
                data_source='sample_data',
                sample_part=part_number
            )
            
            if not success:
                print(f"   ❌ Failed to load data for part {part_number}")
                continue
            
            # Train model
            training_success = forecaster.train_model()
            
            if not training_success:
                print(f"   ❌ Failed to train model for part {part_number}")
                continue
            
            # Validate model
            validation_results = forecaster.validate_model(test_size=0.3)
            
            if validation_results:
                accuracy = validation_results.get('accuracy', 0)
                mape = validation_results.get('mape', 0)
                rmse = validation_results.get('rmse', 0)
                
                print(f"   ✅ Model trained successfully!")
                print(f"   📈 Accuracy: {accuracy:.1f}%")
                print(f"   📊 MAPE: {mape:.2f}%")
                print(f"   📉 RMSE: {rmse:.2f}")
                
                # Generate a short forecast
                forecast_result = forecaster.generate_forecast(
                    horizon="7 days",
                    confidence_level=0.95
                )
                
                if forecast_result and forecast_result.get('success'):
                    forecast_data = forecast_result['forecast']
                    avg_forecast = sum(forecast_data['forecast_values']) / len(forecast_data['forecast_values'])
                    print(f"   🔮 7-day avg forecast: ${avg_forecast:.2f}")
                
                # Store results
                results.append({
                    'part_number': part_number,
                    'time_period': metadata.get('time_period', {}).get('month', 'Unknown'),
                    'transaction_count': transaction_count,
                    'accuracy': accuracy,
                    'mape': mape,
                    'rmse': rmse,
                    'model_params': forecaster.model_params,
                    'avg_forecast': avg_forecast if 'avg_forecast' in locals() else None
                })
                
            else:
                print(f"   ❌ Model validation failed for part {part_number}")
        
        except Exception as e:
            print(f"   ❌ Error testing part {part_number}: {e}")
        
        print()
    
    # Summary of results
    print("📋 TESTING SUMMARY")
    print("=" * 60)
    
    if results:
        print(f"✅ Successfully tested {len(results)} out of {len(available_parts)} sample parts")
        print()
        
        # Sort by accuracy
        results.sort(key=lambda x: x['accuracy'], reverse=True)
        
        print("🏆 PERFORMANCE RANKING:")
        print(f"{'Rank':<4} {'Part':<6} {'Period':<12} {'Accuracy':<10} {'MAPE':<8} {'RMSE':<10} {'Transactions':<12}")
        print("-" * 70)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['part_number']:<6} {result['time_period']:<12} "
                  f"{result['accuracy']:.1f}%{'':<4} {result['mape']:.2f}%{'':<3} "
                  f"{result['rmse']:.2f}{'':<4} {result['transaction_count']:<12}")
        
        print()
        
        # Best and worst performers
        best = results[0]
        worst = results[-1]
        
        print(f"🥇 BEST PERFORMER: Part {best['part_number']} ({best['time_period']})")
        print(f"   Accuracy: {best['accuracy']:.1f}%, MAPE: {best['mape']:.2f}%")
        
        print(f"🔴 NEEDS IMPROVEMENT: Part {worst['part_number']} ({worst['time_period']})")
        print(f"   Accuracy: {worst['accuracy']:.1f}%, MAPE: {worst['mape']:.2f}%")
        
        print()
        
        # Average performance
        avg_accuracy = sum(r['accuracy'] for r in results) / len(results)
        avg_mape = sum(r['mape'] for r in results) / len(results)
        
        print(f"📊 AVERAGE PERFORMANCE:")
        print(f"   Average Accuracy: {avg_accuracy:.1f}%")
        print(f"   Average MAPE: {avg_mape:.2f}%")
        
    else:
        print("❌ No successful tests completed")
    
    print()
    print("💡 USAGE RECOMMENDATIONS:")
    print("1. Use high-accuracy parts for critical business forecasts")
    print("2. Use different parts to test model robustness across time periods")
    print("3. Compare performance across seasonal vs non-seasonal periods")
    print("4. Use lower-accuracy parts to test model improvement strategies")

def demonstrate_api_usage():
    """Demonstrate how to use the API with individual sample parts"""
    print("\n🌐 API USAGE EXAMPLES")
    print("=" * 60)
    
    print("📡 Configure prediction with specific sample part:")
    print("""
    POST /configure-prediction
    {
        "metric": "revenue",
        "period": "daily",
        "data_source": "sample_data",
        "sample_part": 3
    }
    """)
    
    print("📡 Get available sample parts:")
    print("""
    GET /available-sample-parts
    
    Response:
    {
        "success": true,
        "available_parts": [
            {
                "part_number": 1,
                "transaction_count": 100,
                "metadata": {
                    "time_period": {"month": "2019-02"}
                }
            }
        ],
        "total_parts": 10
    }
    """)

if __name__ == "__main__":
    test_individual_sample_parts()
    demonstrate_api_usage()
