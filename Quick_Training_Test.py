# Quick test to verify the training fix works
# Run this after applying the Training_Fix.py

print("🧪 Testing the corrected training implementation...")

# Test with a smaller dataset first
try:
    # Create a test forecaster
    test_forecaster = ProductionFinancialForecaster(p=1, d=1, q=1, device=device)
    
    # Use a subset of your data for quick testing
    test_series = time_series.tail(200)  # Use last 200 points for quick test
    
    print(f"📊 Test data length: {len(test_series)}")
    
    # Prepare test data
    test_train, test_val = test_forecaster.prepare_data(test_series, train_ratio=0.8)
    
    print(f"📏 Test train shape: {test_train.shape}")
    print(f"📏 Test val shape: {test_val.shape}")
    
    # Run a short training test (just 10 epochs)
    print("\n🚀 Running quick training test (10 epochs)...")
    
    test_history = test_forecaster.train_model(
        test_train, 
        test_val, 
        epochs=10,  # Just 10 epochs for testing
        patience=5,
        gradient_clip=1.0
    )
    
    if test_history and len(test_history.get('train_losses', [])) > 0:
        print("✅ Training test SUCCESSFUL!")
        print(f"📈 Final train loss: {test_history['train_losses'][-1]:.6f}")
        print(f"📉 Final val loss: {test_history['val_losses'][-1]:.6f}")
        print("\n🎯 The fix is working! You can now run full training.")
    else:
        print("❌ Training test failed - no losses recorded")
        
except Exception as e:
    print(f"❌ Training test failed: {e}")
    import traceback
    traceback.print_exc()
    print("\n🔧 Please check the error above and ensure all fixes are applied correctly.")

print("\n" + "="*60)
print("📝 INSTRUCTIONS FOR FULL TRAINING:")
print("="*60)
print("1. If the test above was successful, run your full training with:")
print("   forecaster.train_model(train_tensor, val_tensor, epochs=1000, patience=100)")
print("")
print("2. You should now see:")
print("   - Proper progress bars")
print("   - Loss values updating")
print("   - No dimension mismatch errors")
print("")
print("3. Training should complete successfully and show final results")
print("="*60)
