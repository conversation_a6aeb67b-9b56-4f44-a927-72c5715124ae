#!/usr/bin/env python3
"""
Simple verification script for all 15 sample parts
"""

import json
import os
from datetime import datetime

def verify_sample_parts():
    """Verify all 15 sample parts are correctly formatted"""
    print("🔍 Verifying All 15 Sample Parts")
    print("=" * 50)
    
    sample_dir = 'sample'
    total_transactions = 0
    all_parts = []
    
    for i in range(1, 16):
        file_path = f'{sample_dir}/part{i}.json'
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                # Extract metadata
                metadata = data.get('metadata', {})
                time_period = metadata.get('time_period', {})
                transactions = data.get('transactions', [])
                
                part_info = {
                    'part': i,
                    'month': time_period.get('month', 'Unknown'),
                    'start_date': time_period.get('start_date', 'Unknown'),
                    'end_date': time_period.get('end_date', 'Unknown'),
                    'transaction_count': len(transactions),
                    'total_revenue': sum(t.get('total_transaction_value', 0) for t in transactions)
                }
                
                all_parts.append(part_info)
                total_transactions += len(transactions)
                
                print(f"✅ Part {i:2d}: {part_info['month']} | {part_info['transaction_count']} transactions | £{part_info['total_revenue']:,.2f}")
                
            except Exception as e:
                print(f"❌ Error reading part {i}: {e}")
        else:
            print(f"❌ Missing part {i}")
    
    print(f"\n📊 Summary:")
    print(f"   Total Parts: {len(all_parts)}/15")
    print(f"   Total Transactions: {total_transactions:,}")
    print(f"   Total Revenue: £{sum(p['total_revenue'] for p in all_parts):,.2f}")
    
    # Show seasonal distribution
    print(f"\n🌟 Seasonal Distribution:")
    seasons = {
        'Winter': [1, 2, 14, 15],  # Jan, Feb
        'Spring': [4, 5, 8, 13],   # Mar, Apr, May
        'Summer': [6, 9, 12],      # Jun, Jul, Aug
        'Autumn': [3, 7, 11],      # Sep, Oct, Nov
        'Holiday': [10, 11, 15]    # Dec, Nov, Jan (shopping seasons)
    }
    
    for season, part_nums in seasons.items():
        season_parts = [p for p in all_parts if p['part'] in part_nums]
        season_transactions = sum(p['transaction_count'] for p in season_parts)
        season_revenue = sum(p['total_revenue'] for p in season_parts)
        print(f"   {season:8s}: {len(season_parts)} parts, {season_transactions} transactions, £{season_revenue:,.2f}")
    
    # Show chronological order
    print(f"\n📅 Chronological Order:")
    sorted_parts = sorted(all_parts, key=lambda x: x['start_date'])
    for part in sorted_parts:
        print(f"   {part['start_date']} to {part['end_date']}: Part {part['part']} ({part['month']})")
    
    return len(all_parts) == 15

def main():
    """Main verification function"""
    print("🚀 SAMPLE PARTS VERIFICATION")
    print("=" * 60)
    
    success = verify_sample_parts()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL 15 SAMPLE PARTS VERIFIED SUCCESSFULLY!")
        print("\n✅ Ready for ARIMA testing with:")
        print("   • 15 different time periods")
        print("   • 1,500 total transactions")
        print("   • Complete seasonal coverage")
        print("   • Diverse shopping patterns")
        print("\n💡 Usage:")
        print("   1. Start API: python app/arima_api.py")
        print("   2. Open web app: http://localhost:3000")
        print("   3. Select 'Sample Data' and choose any part 1-15")
        print("   4. Train models and compare seasonal performance")
    else:
        print("❌ VERIFICATION FAILED!")
        print("   Some sample parts are missing or corrupted")
    
    return success

if __name__ == "__main__":
    main()
