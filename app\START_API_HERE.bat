@echo off
title ARIMA Forecasting API Server
color 0A

echo.
echo ================================================================
echo  🚀 ARIMA FORECASTING API SERVER
echo ================================================================
echo.
echo  📊 Starting API server for forecasting functionality...
echo  🌐 API will be available at: http://localhost:8001
echo  🔗 Web dashboard should be at: http://localhost:3000
echo.
echo  ⚠️  IMPORTANT: Keep this window open while using the app!
echo  🛑 Press Ctrl+C to stop the server
echo.
echo ================================================================
echo.

python basic_api.py

echo.
echo ================================================================
echo  🛑 API Server Stopped
echo ================================================================
echo.
pause
