import pandas as pd
import json
import os
from datetime import datetime
import numpy as np

def create_sample_directory():
    """Create sample directory if it doesn't exist"""
    if not os.path.exists('sample'):
        os.makedirs('sample')
        print("Created 'sample' directory")

def load_and_analyze_data():
    """Load the cleaned transaction data and analyze time periods"""
    print("Loading cleaned transaction data...")
    df = pd.read_csv('cleaned_transaction_data.csv')
    df['transactiontime'] = pd.to_datetime(df['transactiontime'])

    print(f"Total transactions: {len(df)}")
    print(f"Date range: {df['transactiontime'].min()} to {df['transactiontime'].max()}")
    print(f"Years available: {sorted(df['transaction_year'].unique())}")

    return df

def create_time_based_samples(df, num_parts=10, samples_per_part=100):
    """Create time-based samples from different periods"""

    # Filter out outlier dates (keep only 2018-2019 data)
    df_filtered = df[(df['transaction_year'] >= 2018) & (df['transaction_year'] <= 2019)].copy()
    print(f"Filtered data to {len(df_filtered)} transactions (2018-2019 only)")

    # Sort by transaction time
    df_sorted = df_filtered.sort_values('transactiontime').reset_index(drop=True)

    # Create monthly samples instead of yearly to get better distribution
    df_sorted['year_month'] = df_sorted['transactiontime'].dt.to_period('M')
    unique_months = df_sorted['year_month'].unique()

    print(f"Available months: {len(unique_months)}")
    print(f"Date range: {df_sorted['transactiontime'].min()} to {df_sorted['transactiontime'].max()}")

    # Select 10 different months for sampling
    if len(unique_months) >= num_parts:
        selected_months = np.random.choice(unique_months, size=num_parts, replace=False)
    else:
        # If we have fewer months, use all and repeat some
        selected_months = list(unique_months)
        while len(selected_months) < num_parts:
            selected_months.extend(unique_months[:num_parts - len(selected_months)])

    samples = []

    for i, month_period in enumerate(selected_months):
        print(f"\nCreating part {i+1}...")

        # Filter data for this month
        period_data = df_sorted[df_sorted['year_month'] == month_period]

        print(f"Month: {month_period}")
        print(f"Available transactions in month: {len(period_data)}")

        if len(period_data) == 0:
            print(f"No data available for part {i+1}, skipping...")
            continue

        # Randomly sample from this month
        sample_size = min(samples_per_part, len(period_data))
        sampled_data = period_data.sample(n=sample_size, random_state=42+i)

        print(f"Sampled {len(sampled_data)} transactions")

        # Convert to JSON format
        json_data = []
        for _, row in sampled_data.iterrows():
            transaction = {
                'userid': int(row['userid']) if row['userid'] != -1 else None,
                'transactionid': int(row['transactionid']),
                'transactiontime': row['transactiontime'].isoformat(),
                'itemcode': int(row['itemcode']),
                'itemdescription': row['itemdescription'],
                'numberofitemspurchased': int(row['numberofitemspurchased']),
                'costperitem': float(row['costperitem']),
                'country': row['country'],
                'total_transaction_value': float(row['total_transaction_value']),
                'transaction_year': int(row['transaction_year']),
                'transaction_month': int(row['transaction_month']),
                'transaction_day': int(row['transaction_day']),
                'transaction_weekday': int(row['transaction_weekday']),
                'transaction_hour': int(row['transaction_hour'])
            }
            json_data.append(transaction)

        # Get actual date range for this sample
        start_date = sampled_data['transactiontime'].min()
        end_date = sampled_data['transactiontime'].max()

        samples.append({
            'part_number': i + 1,
            'time_period': {
                'month': str(month_period),
                'start_date': start_date.date().isoformat(),
                'end_date': end_date.date().isoformat()
            },
            'sample_size': len(json_data),
            'transactions': json_data
        })

    return samples

def save_samples_to_files(samples):
    """Save each sample part to a separate JSON file"""

    for sample in samples:
        part_num = sample['part_number']
        filename = f"sample/part{part_num}.json"

        # Create a clean structure for the JSON file
        output_data = {
            'metadata': {
                'part_number': part_num,
                'time_period': sample['time_period'],
                'sample_size': sample['sample_size'],
                'generated_at': datetime.now().isoformat()
            },
            'transactions': sample['transactions']
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"Saved {sample['sample_size']} transactions to {filename}")
        print(f"  Time period: {sample['time_period']['start_date']} to {sample['time_period']['end_date']}")

def main():
    """Main function to orchestrate the sampling process"""
    print("=== Transaction Data Sampling Script ===")
    print("Creating 10 JSON parts with up to 100 transactions each from different time periods\n")

    # Create sample directory
    create_sample_directory()

    # Load and analyze data
    df = load_and_analyze_data()

    # Create time-based samples
    samples = create_time_based_samples(df, num_parts=10, samples_per_part=100)

    # Save samples to files
    print(f"\nSaving {len(samples)} sample parts...")
    save_samples_to_files(samples)

    print(f"\n=== Sampling Complete ===")
    print(f"Created {len(samples)} JSON files in the 'sample' directory")
    print("Each file contains up to 100 randomly sampled transactions from different time periods")

if __name__ == "__main__":
    main()
