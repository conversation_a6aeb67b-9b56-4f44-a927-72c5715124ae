#!/usr/bin/env python3
"""
Simple startup script for ARIMA Forecasting Web Application
"""

import subprocess
import threading
import time
import webbrowser
import os
import sys

def start_api_server():
    """Start the API server"""
    print("🚀 Starting API server on port 8001...")
    try:
        subprocess.run([sys.executable, 'simple_api.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 API server stopped")
    except Exception as e:
        print(f"❌ API server error: {e}")

def start_web_server():
    """Start the web server"""
    print("🌐 Starting web server on port 3000...")
    try:
        subprocess.run([sys.executable, 'server.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Web server stopped")
    except Exception as e:
        print(f"❌ Web server error: {e}")

def main():
    """Main function"""
    print("🚀 ARIMA Forecasting Web Application")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('simple_api.py'):
        print("❌ Please run this script from the app/ directory")
        print("   cd app")
        print("   python start_servers.py")
        return
    
    print("📊 Starting both servers...")
    print("   • API Server: http://localhost:8001")
    print("   • Web App: http://localhost:3000")
    print()
    print("🛑 Press Ctrl+C to stop both servers")
    print("=" * 50)
    
    try:
        # Start API server in background thread
        api_thread = threading.Thread(target=start_api_server, daemon=True)
        api_thread.start()
        
        # Wait a moment for API to start
        time.sleep(3)
        
        # Start web server (this will block)
        start_web_server()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down servers...")
        print("Thank you for using the ARIMA Forecasting App!")

if __name__ == "__main__":
    main()
