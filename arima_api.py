"""
ARIMA Forecasting Web API
This module provides a FastAPI web service for ARIMA forecasting
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from contextlib import asynccontextmanager
from arima_forecasting import ARIMAForecaster
from database_manager import DatabaseManager
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variables
forecaster = None
model_loaded = False

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    global forecaster, model_loaded

    logger.info("Starting ARIMA Forecasting API...")

    try:
        forecaster = ARIMAForecaster()

        # Try to load existing model
        if os.path.exists('arima_model.pkl'):
            if forecaster.load_model():
                model_loaded = True
                logger.info("Existing ARIMA model loaded successfully")
            else:
                logger.warning("Failed to load existing model")
        else:
            logger.info("No existing model found. Model will be trained on first forecast request.")

    except Exception as e:
        logger.error(f"Error during startup: {e}")

    yield

    # Shutdown
    logger.info("Shutting down ARIMA Forecasting API...")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="Financial Reports ARIMA Forecasting API",
    description="API for time series forecasting of financial transaction data using ARIMA models",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic models for API requests/responses
class ForecastRequest(BaseModel):
    steps: int = 30
    confidence_interval: float = 0.95
    period: str = "daily"  # daily or monthly
    metric: str = "revenue"  # revenue, transaction_count, quantity

class ForecastResponse(BaseModel):
    forecast_data: List[Dict[str, Any]]
    model_info: Dict[str, Any]
    metadata: Dict[str, Any]

class ModelTrainingRequest(BaseModel):
    period: str = "daily"
    metric: str = "revenue"
    max_p: int = 3
    max_q: int = 3
    force_retrain: bool = False

class DatabaseStatsResponse(BaseModel):
    total_transactions: int
    unique_users: int
    unique_countries: int
    total_revenue: float
    date_range: Dict[str, str]

# Startup is now handled by lifespan context manager

# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Financial Reports ARIMA Forecasting API",
        "version": "1.0.0",
        "status": "running",
        "model_loaded": model_loaded,
        "endpoints": {
            "forecast": "/forecast",
            "train": "/train",
            "stats": "/database-stats",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db_manager = DatabaseManager()
        db_connected = db_manager.connect_to_database()
        if db_connected:
            db_manager.close_connection()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_connected": db_connected,
            "model_loaded": model_loaded
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.get("/database-stats", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get database statistics"""
    try:
        db_manager = DatabaseManager()

        if not db_manager.connect_to_database():
            raise HTTPException(status_code=500, detail="Failed to connect to database")

        summary = db_manager.get_transaction_summary()
        db_manager.close_connection()

        if not summary:
            raise HTTPException(status_code=404, detail="No data found in database")

        return DatabaseStatsResponse(
            total_transactions=summary.get('total_transactions', 0),
            unique_users=summary.get('unique_users', 0),
            unique_countries=summary.get('unique_countries', 0),
            total_revenue=float(summary.get('total_revenue', 0) or 0),
            date_range={
                "start": str(summary.get('earliest_transaction', '')),
                "end": str(summary.get('latest_transaction', ''))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/train")
async def train_model(request: ModelTrainingRequest):
    """Train or retrain the ARIMA model"""
    global forecaster, model_loaded

    try:
        logger.info(f"Training ARIMA model with parameters: {request.model_dump()}")

        if forecaster is None:
            forecaster = ARIMAForecaster()

        # Check if model already exists and force_retrain is False
        if model_loaded and not request.force_retrain:
            return {
                "message": "Model already trained. Use force_retrain=true to retrain.",
                "model_loaded": True,
                "model_params": forecaster.model_params
            }

        # Fetch time series data
        df = forecaster.fetch_time_series_data(period=request.period, metric=request.metric)

        if df.empty:
            raise HTTPException(status_code=404, detail="No time series data found")

        # Train model
        success = forecaster.train_model()

        if not success:
            raise HTTPException(status_code=500, detail="Model training failed")

        # Test model
        test_results = forecaster.test_model()

        # Save model
        forecaster.save_model()
        model_loaded = True

        return {
            "message": "Model trained successfully",
            "model_params": forecaster.model_params,
            "data_points": len(df),
            "test_results": test_results,
            "aic": float(forecaster.fitted_model.aic),
            "bic": float(forecaster.fitted_model.bic)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error training model: {e}")
        raise HTTPException(status_code=500, detail=f"Training failed: {str(e)}")

@app.post("/forecast", response_model=ForecastResponse)
async def generate_forecast(request: ForecastRequest):
    """Generate ARIMA forecast"""
    global forecaster, model_loaded

    try:
        logger.info(f"Generating forecast with parameters: {request.model_dump()}")

        # Initialize forecaster if needed
        if forecaster is None:
            forecaster = ARIMAForecaster()

        # Load or train model if not loaded
        if not model_loaded:
            logger.info("Model not loaded. Training new model...")

            # Fetch data and train model
            df = forecaster.fetch_time_series_data(period=request.period, metric=request.metric)

            if df.empty:
                raise HTTPException(status_code=404, detail="No time series data found")

            success = forecaster.train_model()
            if not success:
                raise HTTPException(status_code=500, detail="Model training failed")

            forecaster.save_model()
            model_loaded = True

        # Generate forecast
        forecast_results = forecaster.forecast(
            steps=request.steps,
            confidence_interval=request.confidence_interval
        )

        if not forecast_results:
            raise HTTPException(status_code=500, detail="Forecast generation failed")

        # Prepare response
        forecast_df = forecast_results['forecast_df']
        forecast_data = []

        for _, row in forecast_df.iterrows():
            forecast_data.append({
                "date": row['date'].strftime('%Y-%m-%d'),
                "forecast": float(row['forecast']),
                "lower_confidence": float(row['lower_ci']),
                "upper_confidence": float(row['upper_ci'])
            })

        model_info = {
            "model_params": forecaster.model_params,
            "aic": float(forecaster.fitted_model.aic),
            "bic": float(forecaster.fitted_model.bic),
            "data_points": len(forecaster.time_series_data)
        }

        metadata = {
            "forecast_steps": request.steps,
            "confidence_interval": request.confidence_interval,
            "period": request.period,
            "metric": request.metric,
            "generated_at": datetime.now().isoformat()
        }

        return ForecastResponse(
            forecast_data=forecast_data,
            model_info=model_info,
            metadata=metadata
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating forecast: {e}")
        raise HTTPException(status_code=500, detail=f"Forecast failed: {str(e)}")

@app.get("/forecast/quick")
async def quick_forecast(
    steps: int = Query(7, description="Number of forecast steps", ge=1, le=365),
    metric: str = Query("revenue", description="Metric to forecast")
):
    """Quick forecast endpoint with query parameters"""

    request = ForecastRequest(steps=steps, metric=metric)
    return await generate_forecast(request)

@app.get("/model/info")
async def get_model_info():
    """Get information about the current model"""
    global forecaster, model_loaded

    if not model_loaded or forecaster is None:
        return {
            "model_loaded": False,
            "message": "No model currently loaded"
        }

    try:
        return {
            "model_loaded": True,
            "model_params": forecaster.model_params,
            "aic": float(forecaster.fitted_model.aic),
            "bic": float(forecaster.fitted_model.bic),
            "data_points": len(forecaster.time_series_data) if forecaster.time_series_data is not None else 0,
            "last_data_point": forecaster.time_series_data.index[-1].strftime('%Y-%m-%d') if forecaster.time_series_data is not None else None
        }
    except Exception as e:
        logger.error(f"Error getting model info: {e}")
        return {
            "model_loaded": model_loaded,
            "error": str(e)
        }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "Not found", "detail": str(exc.detail) if hasattr(exc, 'detail') else "Resource not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": str(exc.detail) if hasattr(exc, 'detail') else "An unexpected error occurred"}
    )

def main():
    """Run the API server"""
    print("="*60)
    print("ARIMA FORECASTING WEB API")
    print("="*60)
    print("Starting FastAPI server...")
    print("API Documentation: http://localhost:8000/docs")
    print("Health Check: http://localhost:8000/health")
    print("Quick Forecast: http://localhost:8000/forecast/quick")
    print()

    uvicorn.run(
        "arima_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
