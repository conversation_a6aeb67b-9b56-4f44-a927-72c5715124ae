"""
Simple Web Server for Financial Reports ML Dashboard
This serves the HTML/CSS/JS files for the dashboard application
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler with CORS support"""
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # Custom logging format
        print(f"[{self.log_date_time_string()}] {format % args}")

def start_web_server(port=3000, auto_open=True):
    """Start the web server for the dashboard"""
    
    # Change to the app directory
    app_dir = Path(__file__).parent
    os.chdir(app_dir)
    
    print("="*60)
    print(" FINANCIAL REPORTS ML - WEB DASHBOARD SERVER")
    print("="*60)
    print(f"Starting web server on port {port}...")
    print(f"Serving files from: {app_dir}")
    
    try:
        # Create server
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"\n✅ Server started successfully!")
            print(f"🌐 Dashboard URL: {server_url}")
            print(f"📁 Serving directory: {app_dir}")
            print("\n📋 Available files:")
            
            # List available files
            for file in app_dir.glob("*"):
                if file.is_file():
                    size = file.stat().st_size / 1024  # KB
                    print(f"   📄 {file.name} ({size:.1f} KB)")
            
            print(f"\n🔗 Direct links:")
            print(f"   • Dashboard: {server_url}/")
            print(f"   • Index: {server_url}/index.html")
            
            print(f"\n⚠️  Note: Make sure the API server is running on http://localhost:8000")
            print(f"   Start API with: python arima_api.py")
            
            # Auto-open browser
            if auto_open:
                def open_browser():
                    time.sleep(1)  # Wait for server to start
                    print(f"\n🚀 Opening browser to {server_url}")
                    webbrowser.open(server_url)
                
                browser_thread = threading.Thread(target=open_browser)
                browser_thread.daemon = True
                browser_thread.start()
            
            print(f"\n🛑 Press Ctrl+C to stop the server")
            print("="*60)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"\n❌ Port {port} is already in use!")
            print(f"   Try a different port or stop the existing server")
        else:
            print(f"\n❌ Error starting server: {e}")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

def check_files():
    """Check if all required files exist"""
    required_files = ['index.html', 'styles.css', 'app.js']
    app_dir = Path(__file__).parent
    
    missing_files = []
    for file in required_files:
        if not (app_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files found")
    return True

def main():
    """Main function"""
    print("Financial Reports ML Dashboard - Web Server")
    print("This server hosts the HTML/CSS/JS dashboard application")
    print()
    
    # Check if files exist
    if not check_files():
        print("Please ensure all dashboard files are in the app/ directory")
        return
    
    # Get port from user or use default
    try:
        port_input = input("Enter port number (default 3000): ").strip()
        port = int(port_input) if port_input else 3000
    except ValueError:
        port = 3000
        print("Invalid port, using default 3000")
    
    # Ask about auto-opening browser
    auto_open_input = input("Auto-open browser? (y/n, default y): ").strip().lower()
    auto_open = auto_open_input != 'n'
    
    # Start server
    start_web_server(port, auto_open)

if __name__ == "__main__":
    main()
