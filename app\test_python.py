#!/usr/bin/env python3
"""
Test Python environment
"""

print("🐍 Testing Python environment...")
print(f"Python is working!")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    
    import os
    print(f"✅ Current directory: {os.getcwd()}")
    
    import json
    print("✅ JSON module available")
    
    from datetime import datetime
    print(f"✅ DateTime module available: {datetime.now()}")
    
    # Test basic HTTP server
    import http.server
    print("✅ HTTP server module available")
    
    # Test Flask if available
    try:
        import flask
        print("✅ Flask available")
    except ImportError:
        print("⚠️ Flask not available - install with: pip install flask")
    
    # Test pandas if available
    try:
        import pandas
        print("✅ Pandas available")
    except ImportError:
        print("⚠️ Pandas not available - install with: pip install pandas")
    
    print("\n🎉 Python environment test completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")

input("Press Enter to continue...")
