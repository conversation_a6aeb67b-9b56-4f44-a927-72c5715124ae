#!/usr/bin/env python3
"""
Startup script for the Enhanced ARIMA Forecasting Web Application
Starts both the API server and web server
"""

import subprocess
import threading
import time
import webbrowser
import os
import sys
from pathlib import Path

def start_api_server():
    """Start the ARIMA API server"""
    print("🚀 Starting ARIMA API server...")
    try:
        # Change to app directory
        os.chdir(Path(__file__).parent)
        
        # Start API server
        subprocess.run([sys.executable, 'arima_api.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 API server stopped")
    except Exception as e:
        print(f"❌ Error starting API server: {e}")

def start_web_server():
    """Start the web server"""
    print("🌐 Starting web server...")
    try:
        # Wait a moment for API server to start
        time.sleep(3)
        
        # Change to app directory
        os.chdir(Path(__file__).parent)
        
        # Start web server
        subprocess.run([sys.executable, 'server.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Web server stopped")
    except Exception as e:
        print(f"❌ Error starting web server: {e}")

def check_dependencies():
    """Check if required dependencies are available"""
    required_packages = [
        'flask', 'flask_cors', 'pandas', 'numpy', 
        'statsmodels', 'scikit-learn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_data_files():
    """Check if data files are available"""
    data_files = [
        '../cleaned_transaction_data.csv',
        '../sample/part1.json'
    ]
    
    available_files = []
    for file_path in data_files:
        if os.path.exists(file_path):
            available_files.append(file_path)
    
    if not available_files:
        print("⚠️ No data files found!")
        print("Expected files:")
        for file_path in data_files:
            print(f"   • {file_path}")
        print("\nThe app will still start but forecasting may not work without data.")
        return False
    
    print("✅ Data files found:")
    for file_path in available_files:
        print(f"   • {file_path}")
    
    return True

def main():
    """Main startup function"""
    print("=" * 70)
    print(" 🚀 ENHANCED ARIMA FORECASTING WEB APPLICATION")
    print("=" * 70)
    print()
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        print("\n❌ Cannot start application due to missing dependencies.")
        return
    
    print("✅ All dependencies available!")
    print()
    
    # Check data files
    print("📊 Checking data files...")
    check_data_files()
    print()
    
    print("🎯 Application Features:")
    print("   • Interactive ARIMA forecasting")
    print("   • Multiple prediction options (revenue, transactions, quantity)")
    print("   • Flexible time periods (daily, weekly, monthly)")
    print("   • Natural language time horizons")
    print("   • Business insights and recommendations")
    print("   • Real-time web dashboard")
    print()
    
    print("🌐 Starting servers...")
    print("   • API Server: http://localhost:8001")
    print("   • Web Dashboard: http://localhost:3000")
    print()
    
    try:
        # Start API server in a separate thread
        api_thread = threading.Thread(target=start_api_server, daemon=True)
        api_thread.start()
        
        # Wait a moment for API to start
        time.sleep(2)
        
        # Start web server in main thread
        start_web_server()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
        print("Thank you for using the Enhanced ARIMA Forecasting App!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
